package com.student.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.student.entity.Student;
import com.student.mapper.StudentMapper;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 学生服务类
 */
@Service
public class StudentService extends ServiceImpl<StudentMapper, Student> {
    
    /**
     * 根据用户ID查找学生
     */
    public Student getByUserId(Long userId) {
        return baseMapper.findByUserId(userId);
    }

    /**
     * 根据班级ID统计学生人数
     */
    public int getStudentCountByClassId(Long classId) {
        return baseMapper.countByClassId(classId);
    }

    /**
     * 根据班级ID获取学生列表
     */
    public List<Student> getStudentsByClassId(Long classId) {
        return baseMapper.findByClassId(classId);
    }

    /**
     * 获取未分配班级的学生列表
     */
    public List<Student> getUnassignedStudents() {
        return baseMapper.findUnassignedStudents();
    }
}
