package com.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.student.entity.Teacher;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 教师数据访问接口
 */
@Mapper
public interface TeacherMapper extends BaseMapper<Teacher> {

    /**
     * 根据用户ID查询教师信息
     */
    @Select("SELECT * FROM teachers WHERE user_id = #{userId}")
    Teacher selectByUserId(@Param("userId") Long userId);

    /**
     * 根据教工号查询教师信息
     */
    @Select("SELECT * FROM teachers WHERE teacher_number = #{teacherNumber}")
    Teacher selectByTeacherNumber(@Param("teacherNumber") String teacherNumber);

    /**
     * 查询所有教师信息
     */
    @Select("SELECT * FROM teachers")
    List<Teacher> selectAllTeachers();

    /**
     * 手动更新教师信息（只更新存在的字段）
     */
    @Update("UPDATE teachers SET teacher_name = #{teacherName}, phone = #{phone}, email = #{email}, department = #{department} WHERE id = #{id}")
    int updateTeacherInfo(Teacher teacher);
}
