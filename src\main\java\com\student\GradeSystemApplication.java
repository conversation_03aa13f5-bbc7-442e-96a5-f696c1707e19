package com.student;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * 学生成绩管理系统启动类
 */
@SpringBootApplication
@MapperScan("com.student.mapper")
@EnableConfigurationProperties
public class GradeSystemApplication {

    public static void main(String[] args) {
        SpringApplication.run(GradeSystemApplication.class, args);
        System.out.println("=====================================");
        System.out.println("学生成绩管理系统启动成功！");
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("=====================================");
    }
}
