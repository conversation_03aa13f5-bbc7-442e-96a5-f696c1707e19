package com.student.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.student.entity.Class;
import com.student.mapper.ClassMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 班级服务类
 */
@Service
@Transactional
public class ClassService extends ServiceImpl<ClassMapper, Class> {
    
    @Autowired
    private ClassMapper classMapper;
    
    /**
     * 获取所有班级
     */
    public List<Class> getAllClasses() {
        return classMapper.selectAllClasses();
    }
    
    /**
     * 根据年级获取班级
     */
    public List<Class> getClassesByGradeYear(Integer gradeYear) {
        return classMapper.selectByGradeYear(gradeYear);
    }
    
    /**
     * 根据专业获取班级
     */
    public List<Class> getClassesByMajor(String major) {
        return classMapper.selectByMajor(major);
    }
    
    /**
     * 根据班主任ID获取班级
     */
    public List<Class> getClassesByTeacherId(Long teacherId) {
        return classMapper.selectByTeacherId(teacherId);
    }
    
    /**
     * 创建班级
     */
    public boolean createClass(Class clazz) {
        return save(clazz);
    }
    
    /**
     * 更新班级信息
     */
    public boolean updateClass(Class clazz) {
        return updateById(clazz);
    }
    
    /**
     * 删除班级
     */
    public boolean deleteClass(Long id) {
        return removeById(id);
    }

    /**
     * 搜索班级
     */
    public List<Class> searchClasses(String className, String major, String gradeYear) {
        return classMapper.searchClasses(className, major, gradeYear);
    }

    /**
     * 清空教师的班主任职务
     */
    public void clearTeacherFromClasses(Long teacherId) {
        classMapper.clearTeacherFromClasses(teacherId);
    }
}
