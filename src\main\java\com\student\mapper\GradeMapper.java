package com.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.student.entity.Grade;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;
import java.util.Map;

/**
 * 成绩Mapper接口
 */
@Mapper
public interface GradeMapper extends BaseMapper<Grade> {
    
    /**
     * 查询学生成绩（包含课程信息）
     */
    @Select("SELECT g.id, g.student_id, g.course_id, " +
            "COALESCE(g.score, 0) as score, " +
            "COALESCE(g.academic_year, '2024-2025') as academic_year, " +
            "COALESCE(g.semester, 1) as semester, " +
            "c.course_name, c.course_code, c.credits " +
            "FROM grades g " +
            "LEFT JOIN courses c ON g.course_id = c.id " +
            "WHERE g.student_id = #{studentId} " +
            "ORDER BY c.course_code")
    List<Map<String, Object>> findStudentGradesWithCourse(Long studentId);
    
    /**
     * 查询所有成绩（包含学生和课程信息）
     */
    @Select("SELECT g.id, g.student_id, g.course_id, g.score, g.academic_year, g.semester, " +
            "COALESCE(g.is_abnormal, 0) as is_abnormal, " +
            "s.student_name, s.student_number, c.course_name, " +
            "cl.class_name, cl.id as class_id, g.academic_year as year " +
            "FROM grades g " +
            "LEFT JOIN students s ON g.student_id = s.id " +
            "LEFT JOIN courses c ON g.course_id = c.id " +
            "LEFT JOIN classes cl ON s.class_id = cl.id " +
            "WHERE g.score IS NOT NULL " +
            "ORDER BY s.student_number, c.course_name")
    List<Map<String, Object>> findAllGradesWithDetails();



    /**
     * 按课程名查询学生成绩
     */
    @Select("SELECT g.id, g.student_id, g.course_id, " +
            "COALESCE(g.score, 0) as score, " +
            "COALESCE(g.academic_year, '2024-2025') as academic_year, " +
            "COALESCE(g.semester, 1) as semester, " +
            "c.course_name, c.course_code, c.credits " +
            "FROM grades g " +
            "LEFT JOIN courses c ON g.course_id = c.id " +
            "WHERE g.student_id = #{studentId} AND c.course_name LIKE CONCAT('%', #{courseName}, '%') " +
            "ORDER BY c.course_code")
    List<Map<String, Object>> findStudentGradesByCourse(Long studentId, String courseName);

    /**
     * 按多条件查询成绩（教师用）
     */
    @Select("<script>" +
            "SELECT g.id, g.student_id, g.course_id, g.score, g.academic_year, g.semester, " +
            "s.student_name, s.student_number, c.course_name, cl.class_name, cl.id as class_id, " +
            "g.academic_year as year " +
            "FROM grades g " +
            "LEFT JOIN students s ON g.student_id = s.id " +
            "LEFT JOIN courses c ON g.course_id = c.id " +
            "LEFT JOIN classes cl ON s.class_id = cl.id " +
            "WHERE 1=1 " +
            "<if test='studentName != null and studentName != \"\"'>" +
            "AND s.student_name LIKE CONCAT('%', #{studentName}, '%') " +
            "</if>" +
            "<if test='courseName != null and courseName != \"\"'>" +
            "AND c.course_name LIKE CONCAT('%', #{courseName}, '%') " +
            "</if>" +
            "<if test='studentNumber != null and studentNumber != \"\"'>" +
            "AND s.student_number LIKE CONCAT('%', #{studentNumber}, '%') " +
            "</if>" +
            "</script>")
    List<Map<String, Object>> findGradesByConditions(String studentName, String courseName, String studentNumber);

    /**
     * 按班级ID查询成绩
     */
    @Select("SELECT g.id, g.student_id, g.course_id, g.score, g.academic_year, g.semester, " +
            "s.student_name, s.student_number, c.course_name, cl.class_name, cl.id as class_id, " +
            "g.academic_year as year " +
            "FROM grades g " +
            "LEFT JOIN students s ON g.student_id = s.id " +
            "LEFT JOIN courses c ON g.course_id = c.id " +
            "LEFT JOIN classes cl ON s.class_id = cl.id " +
            "WHERE s.class_id = #{classId}")
    List<Map<String, Object>> findGradesByClassId(Long classId);

    /**
     * 查询学生课程表
     */
    @Select("SELECT DISTINCT c.course_name, c.classroom, c.credits, t.teacher_name " +
            "FROM grades g " +
            "LEFT JOIN courses c ON g.course_id = c.id " +
            "LEFT JOIN teachers t ON c.teacher_id = t.id " +
            "WHERE g.student_id = #{studentId} " +
            "ORDER BY c.course_name")
    List<Map<String, Object>> findStudentCourseSchedule(Long studentId);

    /**
     * 根据教师姓名查询该教师授课的课程列表
     */
    @Select("SELECT DISTINCT c.id, c.course_name, c.course_code " +
            "FROM courses c " +
            "LEFT JOIN teachers t ON c.teacher_id = t.id " +
            "WHERE t.teacher_name = #{teacherName}")
    List<Map<String, Object>> findCoursesByTeacherName(String teacherName);

    /**
     * 根据教师姓名和课程ID查询该课程的学生成绩
     */
    @Select("SELECT g.id, g.student_id, g.course_id, g.score, g.academic_year, g.semester, " +
            "s.student_name, s.student_number, c.course_name, cl.class_name, cl.id as class_id, " +
            "g.academic_year as year " +
            "FROM grades g " +
            "LEFT JOIN students s ON g.student_id = s.id " +
            "LEFT JOIN courses c ON g.course_id = c.id " +
            "LEFT JOIN classes cl ON s.class_id = cl.id " +
            "LEFT JOIN teachers t ON c.teacher_id = t.id " +
            "WHERE t.teacher_name = #{teacherName} AND c.id = #{courseId}")
    List<Map<String, Object>> findGradesByTeacherAndCourse(String teacherName, Long courseId);

    /**
     * 根据教师ID查询该教师授课的课程列表
     */
    @Select("SELECT DISTINCT c.id, c.course_name, c.course_code " +
            "FROM courses c " +
            "WHERE c.teacher_id = #{teacherId}")
    List<Map<String, Object>> findCoursesByTeacherId(Long teacherId);

    /**
     * 根据教师ID和课程ID查询该课程的学生成绩
     * 显示所有学生，包括没有成绩记录的学生
     */
    @Select("SELECT " +
            "COALESCE(g.id, 0) as id, " +
            "s.id as student_id, " +
            "c.id as course_id, " +
            "COALESCE(g.score, '') as score, " +
            "COALESCE(g.academic_year, '2024-2025') as academic_year, " +
            "COALESCE(g.semester, 1) as semester, " +
            "COALESCE(s.student_name, '未知') as student_name, " +
            "COALESCE(s.student_number, '未知') as student_number, " +
            "COALESCE(c.course_name, '未知') as course_name, " +
            "COALESCE(cl.class_name, '未知') as class_name, " +
            "COALESCE(cl.id, 0) as class_id, " +
            "COALESCE(g.academic_year, '2024-2025') as year " +
            "FROM students s " +
            "CROSS JOIN courses c " +
            "LEFT JOIN grades g ON s.id = g.student_id AND c.id = g.course_id " +
            "LEFT JOIN classes cl ON s.class_id = cl.id " +
            "WHERE c.teacher_id = #{teacherId} AND c.id = #{courseId} " +
            "ORDER BY s.student_number")
    List<Map<String, Object>> findGradesByTeacherIdAndCourse(Long teacherId, Long courseId);

    /**
     * 查询学生的课程信息（包含教师信息）
     * 显示所有课程，不仅仅是有成绩的课程
     */
    @Select("SELECT DISTINCT " +
            "COALESCE(c.course_name, '未设置') as courseName, " +
            "COALESCE(t.teacher_name, '未分配') as teacherName, " +
            "COALESCE(c.classroom, '待安排') as classroom, " +
            "COALESCE(c.credits, 0) as credits, " +
            "COALESCE(c.teaching_hours, 0) as teachingHours, " +
            "CASE WHEN g.id IS NOT NULL THEN '已录入' ELSE '未录入' END as gradeStatus " +
            "FROM courses c " +
            "LEFT JOIN teachers t ON c.teacher_id = t.id " +
            "LEFT JOIN grades g ON c.id = g.course_id AND g.student_id = #{studentId} " +
            "ORDER BY courseName")
    List<Map<String, Object>> findStudentCoursesWithTeacher(Long studentId);

    /**
     * 删除学生的所有成绩记录
     */
    @Delete("DELETE FROM grades WHERE student_id = #{studentId}")
    void deleteGradesByStudentId(Long studentId);
}
