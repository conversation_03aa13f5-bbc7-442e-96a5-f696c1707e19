package com.student.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.student.entity.Teacher;
import com.student.mapper.TeacherMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教师服务类
 */
@Service
public class TeacherService extends ServiceImpl<TeacherMapper, Teacher> {

    /**
     * 根据用户ID查询教师信息
     */
    public Teacher getByUserId(Long userId) {
        return baseMapper.selectByUserId(userId);
    }

    /**
     * 根据教工号查询教师信息
     */
    public Teacher getByTeacherNumber(String teacherNumber) {
        return baseMapper.selectByTeacherNumber(teacherNumber);
    }

    /**
     * 查询所有教师信息
     */
    public List<Teacher> getAllTeachers() {
        return baseMapper.selectAllTeachers();
    }

    /**
     * 手动更新教师信息（避免MyBatis-Plus自动字段问题）
     */
    public boolean updateTeacherInfo(Teacher teacher) {
        return baseMapper.updateTeacherInfo(teacher) > 0;
    }
}
