<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加课程 - 管理员</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .header {
            background-color: #dc3545;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }

        .btn-primary {
            background-color: #dc3545;
            color: white;
        }

        .btn-primary:hover {
            background-color: #c82333;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
            text-decoration: none;
        }

        .btn-secondary:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .alert {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .logout:hover {
            background-color: rgba(255, 255, 255, 0.3);
            text-decoration: none;
            color: white;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/admin/courses" class="back-btn">← 返回课程管理</a>

            <h2>添加课程</h2>

            <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>

            <form method="post" action="/admin/courses/add">
                <div class="form-row">
                    <div class="form-group">
                        <label for="courseName">课程名称：</label>
                        <input type="text" id="courseName" name="courseName" required placeholder="例如：高等数学">
                    </div>
                    <div class="form-group">
                        <label for="courseCode">课程代码：</label>
                        <input type="text" id="courseCode" name="courseCode" required placeholder="例如：MATH001">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="credits">学分：</label>
                        <input type="number" id="credits" name="credits" required min="1" max="10" placeholder="例如：4">
                    </div>
                    <div class="form-group">
                        <label for="teachingHours">学时：</label>
                        <input type="number" id="teachingHours" name="teachingHours" required min="1" max="200"
                            placeholder="例如：64">
                    </div>
                </div>

                <div class="form-group">
                    <label for="teacherId">授课教师：</label>
                    <select id="teacherId" name="teacherId">
                        <option value="">请选择授课教师</option>
                        <option th:each="teacher : ${teachers}" th:value="${teacher.id}"
                            th:text="${teacher.teacherName}">教师姓名</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="classroom">教室：</label>
                    <input type="text" id="classroom" name="classroom" required placeholder="例如：教学楼A101">
                </div>

                <div>
                    <button type="submit" class="btn btn-primary">添加课程</button>
                    <a href="/admin/courses" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</body>

</html>