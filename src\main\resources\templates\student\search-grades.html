<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成绩查询 - 学生成绩管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .search-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e1e1;
            border-radius: 6px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background-color: #0056b3;
            text-decoration: none;
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        tr:hover {
            background-color: #f5f5f5;
        }

        .score {
            font-weight: bold;
            color: #28a745;
        }

        .score.low {
            color: #dc3545;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #0056b3;
            text-decoration: none;
            color: white;
        }

        .nav-links {
            text-align: left;
        }

        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="nav-links">
            <a href="/student/dashboard" class="back-btn">← 返回主页</a>
        </div>

        <div class="header">
            <h1>个人成绩</h1>
            <p th:text="'欢迎，' + ${user.realName} + ' (' + ${student.studentNumber} + ')'">欢迎，学生</p>
        </div>

        <div class="search-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="courseName">课程名称：</label>
                    <input type="text" id="courseName" name="courseName" th:value="${courseName}"
                        placeholder="输入课程名称进行搜索" onkeyup="searchTable()">
                </div>
            </div>
        </div>

        <div class="table-container">
            <table id="gradesTable" th:if="${grades != null and !grades.isEmpty()}">
                <thead>
                    <tr>
                        <th>课程编号</th>
                        <th>课程名称</th>
                        <th>学年</th>
                        <th>学期</th>
                        <th>学分</th>
                        <th>成绩</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="grade : ${grades}">
                        <td th:text="${grade.course_code}">课程编号</td>
                        <td th:text="${grade.course_name}">课程名称</td>
                        <td th:text="${grade.academic_year}">学年</td>
                        <td th:text="${grade.semester}">学期</td>
                        <td th:text="${grade.credits}">学分</td>
                        <td>
                            <span th:text="${grade.score}"
                                th:class="${grade.score < 60} ? 'score low' : 'score'">成绩</span>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div th:if="${grades == null or grades.isEmpty()}" class="no-data">
                <p>暂无成绩数据</p>
            </div>
        </div>


    </div>

    <script>
        function searchTable() {
            var courseFilter = document.getElementById("courseName").value.toUpperCase();
            var table = document.getElementById("gradesTable");
            var tr = table.getElementsByTagName("tr");

            for (var i = 1; i < tr.length; i++) {
                var courseName = tr[i].getElementsByTagName("td")[1]; // 课程名称

                if (courseName) {
                    var courseText = courseName.textContent || courseName.innerText;

                    if (courseText.toUpperCase().indexOf(courseFilter) > -1) {
                        tr[i].style.display = "";
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            }
        }
    </script>
</body>

</html>