<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑成绩 - 教师管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            margin-bottom: 20px;
        }
        
        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #6c7ae0;
        }
        
        .form-group input[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        
        .btn {
            padding: 12px 30px;
            background-color: #6c7ae0;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background-color: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background-color: #6c757d;
            margin-left: 10px;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/teacher/search-grades" class="back-btn">← 返回成绩列表</a>
        
        <div class="header">
            <h1>编辑成绩</h1>
            <p th:text="'欢迎，' + ${user.realName}">欢迎，教师</p>
        </div>
        
        <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>
        
        <form method="post" action="/teacher/update-grade">
            <input type="hidden" name="gradeId" th:value="${grade.id}">
            
            <div class="form-group">
                <label>学生ID：</label>
                <input type="text" th:value="${grade.studentId}" readonly>
            </div>
            
            <div class="form-group">
                <label>课程ID：</label>
                <input type="text" th:value="${grade.courseId}" readonly>
            </div>
            
            <div class="form-group">
                <label for="score">成绩：</label>
                <input type="number" id="score" name="score" 
                       th:value="${grade.score}" 
                       min="0" max="100" step="0.1" required>
            </div>
            
            <div class="form-group">
                <label for="academicYear">学年：</label>
                <input type="text" id="academicYear" name="academicYear" 
                       th:value="${grade.academicYear}" 
                       placeholder="例如：2024-2025" required>
            </div>
            
            <div class="form-group">
                <label for="semester">学期：</label>
                <select id="semester" name="semester" required>
                    <option value="">请选择学期</option>
                    <option value="1" th:selected="${grade.semester == 1}">第一学期</option>
                    <option value="2" th:selected="${grade.semester == 2}">第二学期</option>
                </select>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn">更新成绩</button>
                <a href="/teacher/search-grades" class="btn btn-secondary">取消</a>
            </div>
        </form>
    </div>
</body>
</html>
