<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .header {
            background-color: #dc3545;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .role-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .role-student {
            background-color: #007bff;
            color: white;
        }

        .role-teacher {
            background-color: #28a745;
            color: white;
        }

        .role-admin {
            background-color: #dc3545;
            color: white;
        }

        .add-btn {
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .add-btn:hover {
            background-color: #218838;
        }

        .search-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .search-form .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .search-form .form-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }

        .form-actions-row {
            margin-top: 15px;
            text-align: center;
        }

        .search-form label {
            margin-bottom: 5px;
            font-weight: bold;
        }

        .search-form input,
        .search-form select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-btn,
        .reset-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            height: fit-content;
        }

        .search-btn {
            background-color: #007bff;
            color: white;
        }

        .search-btn:hover {
            background-color: #0056b3;
        }

        .reset-btn {
            background-color: #6c757d;
            color: white;
            margin-left: 10px;
        }

        .reset-btn:hover {
            background-color: #545b62;
        }

        .action-btn {
            padding: 5px 10px;
            margin-right: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 12px;
        }

        .btn-view {
            background-color: #17a2b8;
            color: white;
        }

        .btn-edit {
            background-color: #007bff;
            color: white;
        }

        .btn-delete {
            background-color: #dc3545;
            color: white;
        }

        .btn-view:hover {
            background-color: #138496;
        }

        .btn-edit:hover {
            background-color: #0056b3;
        }

        .btn-delete:hover {
            background-color: #c82333;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .form-actions {
            text-align: right;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .role-specific {
            display: none;
        }

        /* 消息样式 */
        .alert {
            padding: 12px 20px;
            margin: 15px 0;
            border-radius: 4px;
            font-weight: 500;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/admin/dashboard" class="back-btn">← 返回主页</a>

            <h2>用户管理</h2>

            <!-- 消息显示区域 -->
            <div th:if="${param.message}" class="alert alert-success" th:text="${param.message}"></div>
            <div th:if="${param.error}" class="alert alert-error" th:text="${param.error}"></div>

            <!-- 搜索框 -->
            <div class="search-box" style="margin: 20px 0;">
                <input type="text" id="searchRealName" placeholder="按姓名搜索..." onkeyup="searchTable()"
                    style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px; width: 200px;">
                <input type="text" id="searchUsername" placeholder="按用户名搜索..." onkeyup="searchTable()"
                    style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px; width: 200px;">
                <select id="searchRole" onchange="searchTable()"
                    style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 150px;">
                    <option value="">全部身份</option>
                    <option value="STUDENT">学生</option>
                    <option value="TEACHER">教师</option>
                    <option value="ADMIN">管理员</option>
                </select>
            </div>

            <button class="add-btn" onclick="showAddUserModal()">+ 添加用户</button>

            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>真实姓名</th>
                        <th>身份</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="u : ${users}">
                        <td th:text="${u.id}"></td>
                        <td th:text="${u.username}"></td>
                        <td th:text="${u.realName}"></td>
                        <td>
                            <span th:class="'role-badge role-' + ${#strings.toLowerCase(u.role)}"
                                th:text="${u.role == 'STUDENT' ? '学生' : (u.role == 'TEACHER' ? '教师' : '管理员')}"></span>
                        </td>
                        <td>
                            <button onclick="viewUser(this)" th:data-user-id="${u.id}" th:data-username="${u.username}"
                                th:data-realname="${u.realName}" th:data-role="${u.role}"
                                class="action-btn btn-view">查看</button>
                            <button onclick="editUser(this)" th:data-user-id="${u.id}" th:data-username="${u.username}"
                                th:data-realname="${u.realName}" th:data-role="${u.role}"
                                class="action-btn btn-edit">编辑</button>
                            <button onclick="deleteUser(this)" th:data-user-id="${u.id}"
                                th:data-username="${u.username}" class="action-btn btn-delete">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div th:if="${#lists.isEmpty(users)}" style="text-align: center; padding: 50px; color: #666;">
                暂无用户记录
            </div>
        </div>
    </div>

    <!-- 添加用户模态框 -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加用户</h3>
                <span class="close" onclick="closeModal('addUserModal')">&times;</span>
            </div>
            <form action="/admin/users/add" method="post">
                <div class="form-group">
                    <label for="addUsername">用户名 *</label>
                    <input type="text" id="addUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="addPassword">密码 *</label>
                    <input type="password" id="addPassword" name="password" required>
                </div>
                <div class="form-group">
                    <label for="addRealName">真实姓名 *</label>
                    <input type="text" id="addRealName" name="realName" required>
                </div>
                <div class="form-group">
                    <label for="addRole">身份 *</label>
                    <select id="addRole" name="role" onchange="toggleRoleFields('add')" required>
                        <option value="">请选择身份</option>
                        <option value="STUDENT">学生</option>
                        <option value="TEACHER">教师</option>
                    </select>
                </div>

                <!-- 学生特有字段 -->
                <div id="addStudentFields" class="role-specific">
                    <div class="form-group">
                        <label for="addStudentNumber">学号</label>
                        <input type="text" id="addStudentNumber" name="studentNumber">
                    </div>
                    <div class="form-group">
                        <label for="addClassId">班级</label>
                        <select id="addClassId" name="classId">
                            <option value="">请选择班级</option>
                            <option th:each="c : ${classes}" th:value="${c.id}" th:text="${c.className}"></option>
                        </select>
                    </div>
                </div>

                <!-- 教师特有字段 -->
                <div id="addTeacherFields" class="role-specific">
                    <div class="form-group">
                        <label for="addTeacherNumber">教工号</label>
                        <input type="text" id="addTeacherNumber" name="teacherNumber">
                    </div>
                    <div class="form-group">
                        <label for="addPhone">电话</label>
                        <input type="text" id="addPhone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="addEmail">邮箱</label>
                        <input type="email" id="addEmail" name="email">
                    </div>
                    <div class="form-group">
                        <label for="addDepartment">学院</label>
                        <input type="text" id="addDepartment" name="department">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal('addUserModal')">取消</button>
                    <button type="submit" class="btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 查看用户模态框 -->
    <div id="viewUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>用户详情</h3>
                <span class="close" onclick="closeModal('viewUserModal')">&times;</span>
            </div>
            <div id="viewUserContent">
                <!-- 用户详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn-secondary" onclick="closeModal('viewUserModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑用户</h3>
                <span class="close" onclick="closeModal('editUserModal')">&times;</span>
            </div>
            <form id="editUserForm" action="/admin/users/update" method="post">
                <input type="hidden" id="editUserId" name="userId">

                <div class="form-group">
                    <label for="editUsername">用户名 *</label>
                    <input type="text" id="editUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="editRealName">真实姓名 *</label>
                    <input type="text" id="editRealName" name="realName" required>
                </div>
                <div class="form-group">
                    <label for="editPassword">新密码（留空则不修改）</label>
                    <input type="password" id="editPassword" name="password" placeholder="留空则不修改密码">
                </div>
                <div class="form-group">
                    <label for="editConfirmPassword">确认密码</label>
                    <input type="password" id="editConfirmPassword" placeholder="请再次输入新密码">
                </div>
                <div class="form-group">
                    <label for="editPhone">电话号码</label>
                    <input type="text" id="editPhone" name="phone" placeholder="请输入电话号码">
                </div>
                <div class="form-group">
                    <label for="editEmail">邮箱地址</label>
                    <input type="email" id="editEmail" name="email" placeholder="请输入邮箱地址">
                </div>
                <div class="form-group" id="editDepartmentGroup">
                    <label for="editDepartment">学院</label>
                    <input type="text" id="editDepartment" name="department" placeholder="请输入学院">
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal('editUserModal')">取消</button>
                    <button type="submit" class="btn-primary">保存修改</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function searchTable() {
            var realNameFilter = document.getElementById("searchRealName").value.toUpperCase();
            var usernameFilter = document.getElementById("searchUsername").value.toUpperCase();
            var roleFilter = document.getElementById("searchRole").value;
            var table = document.querySelector("table");
            var tr = table.getElementsByTagName("tr");

            for (var i = 1; i < tr.length; i++) {
                var username = tr[i].getElementsByTagName("td")[1];
                var realName = tr[i].getElementsByTagName("td")[2];
                var roleSpan = tr[i].getElementsByTagName("td")[3].getElementsByTagName("span")[0];

                if (username && realName && roleSpan) {
                    var usernameText = username.textContent || username.innerText;
                    var realNameText = realName.textContent || realName.innerText;
                    var roleText = roleSpan.textContent || roleSpan.innerText;

                    // 将角色文本转换为英文值进行比较
                    var roleValue = "";
                    if (roleText === "学生") roleValue = "STUDENT";
                    else if (roleText === "教师") roleValue = "TEACHER";
                    else if (roleText === "管理员") roleValue = "ADMIN";

                    var showRow = true;

                    if (realNameFilter && realNameText.toUpperCase().indexOf(realNameFilter) === -1) {
                        showRow = false;
                    }

                    if (usernameFilter && usernameText.toUpperCase().indexOf(usernameFilter) === -1) {
                        showRow = false;
                    }

                    if (roleFilter && roleValue !== roleFilter) {
                        showRow = false;
                    }

                    tr[i].style.display = showRow ? "" : "none";
                }
            }
        }

        function showAddUserModal() {
            document.getElementById('addUserModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function viewUser(btn) {
            var userId = btn.getAttribute('data-user-id');
            var username = btn.getAttribute('data-username');
            var realName = btn.getAttribute('data-realname');
            var role = btn.getAttribute('data-role');

            // 发送AJAX请求获取用户详细信息
            fetch('/admin/users/view/' + userId + '/data')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayUserDetails(data.userDetails);
                        document.getElementById('viewUserModal').style.display = 'block';
                    } else {
                        alert('获取用户信息失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取用户信息失败');
                });
        }

        function displayUserDetails(userDetails) {
            var content = '<div class="info-section">';
            content += '<h4>基本信息</h4>';
            content += '<table style="width: 100%; border-collapse: collapse;">';
            content += '<tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">姓名</td><td style="padding: 8px; border: 1px solid #ddd;">' + userDetails.user.realName + '</td></tr>';

            if (userDetails.student) {
                content += '<tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">学号</td><td style="padding: 8px; border: 1px solid #ddd;">' + (userDetails.student.studentNumber || '未设置') + '</td></tr>';
                content += '<tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">班级</td><td style="padding: 8px; border: 1px solid #ddd;">' + (userDetails.className || '未分配') + '</td></tr>';
                if (userDetails.className) {
                    content += '<tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">专业</td><td style="padding: 8px; border: 1px solid #ddd;">' + (userDetails.major || '未设置') + '</td></tr>';
                    content += '<tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">年级</td><td style="padding: 8px; border: 1px solid #ddd;">' + (userDetails.gradeYear || '未设置') + '</td></tr>';
                }
            }

            if (userDetails.teacher) {
                content += '<tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">教工号</td><td style="padding: 8px; border: 1px solid #ddd;">' + (userDetails.teacher.teacherNumber || '未设置') + '</td></tr>';
                if (userDetails.teacher.phone) {
                    content += '<tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">电话</td><td style="padding: 8px; border: 1px solid #ddd;">' + userDetails.teacher.phone + '</td></tr>';
                }
                if (userDetails.teacher.email) {
                    content += '<tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">邮箱</td><td style="padding: 8px; border: 1px solid #ddd;">' + userDetails.teacher.email + '</td></tr>';
                }
                if (userDetails.teacher.department) {
                    content += '<tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">学院</td><td style="padding: 8px; border: 1px solid #ddd;">' + userDetails.teacher.department + '</td></tr>';
                }
            }

            // 管理员只显示姓名
            content += '</table>';
            content += '</div>';
            document.getElementById('viewUserContent').innerHTML = content;
        }

        function editUser(btn) {
            var userId = btn.getAttribute('data-user-id');
            var username = btn.getAttribute('data-username');
            var realName = btn.getAttribute('data-realname');
            var role = btn.getAttribute('data-role');

            // 填充编辑表单基本信息
            document.getElementById('editUserId').value = userId;
            document.getElementById('editUsername').value = username;
            document.getElementById('editRealName').value = realName;

            // 重置密码字段
            document.getElementById('editPassword').value = '';
            document.getElementById('editConfirmPassword').value = '';

            // 重置phone、email和department字段
            document.getElementById('editPhone').value = '';
            document.getElementById('editEmail').value = '';
            document.getElementById('editDepartment').value = '';

            // 根据用户身份显示/隐藏相关字段
            var phoneGroup = document.getElementById('editPhone').parentElement;
            var emailGroup = document.getElementById('editEmail').parentElement;
            var departmentGroup = document.getElementById('editDepartmentGroup');

            if (role === 'TEACHER') {
                // 教师用户：显示phone、email和department字段
                phoneGroup.style.display = 'block';
                emailGroup.style.display = 'block';
                departmentGroup.style.display = 'block';

                // 发送AJAX请求获取教师详细信息
                fetch('/admin/users/view/' + userId + '/data')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.userDetails.teacher) {
                            document.getElementById('editPhone').value = data.userDetails.teacher.phone || '';
                            document.getElementById('editEmail').value = data.userDetails.teacher.email || '';
                            document.getElementById('editDepartment').value = data.userDetails.teacher.department || '';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading teacher details:', error);
                    });
            } else {
                // 学生和管理员用户：隐藏phone、email和department字段
                phoneGroup.style.display = 'none';
                emailGroup.style.display = 'none';
                departmentGroup.style.display = 'none';
            }

            document.getElementById('editUserModal').style.display = 'block';
        }

        function getRoleText(role) {
            switch (role) {
                case 'STUDENT': return '学生';
                case 'TEACHER': return '教师';
                case 'ADMIN': return '管理员';
                default: return role;
            }
        }

        function toggleRoleFields(prefix) {
            var role = document.getElementById(prefix + 'Role').value;
            var studentFields = document.getElementById(prefix + 'StudentFields');
            var teacherFields = document.getElementById(prefix + 'TeacherFields');

            // 隐藏所有角色特定字段
            studentFields.style.display = 'none';
            teacherFields.style.display = 'none';

            // 显示对应角色的字段
            if (role === 'STUDENT') {
                studentFields.style.display = 'block';
            } else if (role === 'TEACHER') {
                teacherFields.style.display = 'block';
            }
        }

        // 编辑表单提交前验证
        document.getElementById('editUserForm').addEventListener('submit', function (e) {
            var password = document.getElementById('editPassword').value;
            var confirmPassword = document.getElementById('editConfirmPassword').value;

            // 如果输入了密码，则需要验证
            if (password.length > 0 || confirmPassword.length > 0) {
                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert('两次输入的密码不一致，请重新输入！');
                    return false;
                }
            }
        });

        function deleteUser(btn) {
            var userId = btn.getAttribute('data-user-id');

            if (confirm("是否确认删除？")) {
                // 创建表单并提交
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/users/delete/' + userId;
                document.body.appendChild(form);
                form.submit();
            }
        }



        // 点击模态框外部关闭
        window.onclick = function (event) {
            var modals = document.getElementsByClassName('modal');
            for (var i = 0; i < modals.length; i++) {
                if (event.target == modals[i]) {
                    modals[i].style.display = 'none';
                }
            }
        }
    </script>
</body>

</html>