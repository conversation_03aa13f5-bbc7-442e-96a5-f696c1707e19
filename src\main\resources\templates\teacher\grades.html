<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成绩管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .header {
            background-color: #28a745;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .edit-btn {
            padding: 5px 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .edit-btn:hover {
            background-color: #0056b3;
        }

        .search-box {
            margin-bottom: 20px;
        }

        .search-box input,
        .search-box select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 0;
            border-radius: 8px;
            width: 400px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background-color: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-cancel,
        .btn-submit {
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .btn-cancel {
            background-color: #6c757d;
            color: white;
        }

        .btn-cancel:hover {
            background-color: #545b62;
        }

        .btn-submit {
            background-color: #28a745;
            color: white;
        }

        .btn-submit:hover {
            background-color: #218838;
        }

        /* 消息样式 */
        .alert {
            padding: 12px 20px;
            margin: 15px 0;
            border-radius: 4px;
            font-weight: 500;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/teacher/dashboard" class="back-btn">← 返回主页</a>

            <h2>成绩管理</h2>

            <!-- 消息显示区域 -->
            <div th:if="${message}" class="alert alert-success" th:text="${message}"></div>
            <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>

            <div class="search-box">
                <input type="text" id="searchStudent" placeholder="按学生姓名搜索..." onkeyup="searchTable()">
                <input type="text" id="searchNumber" placeholder="按学号搜索..." onkeyup="searchTable()">
            </div>

            <table id="gradesTable">
                <thead>
                    <tr>
                        <th>学号</th>
                        <th>学生姓名</th>
                        <th>课程名称</th>
                        <th>学年</th>
                        <th>学期</th>
                        <th>成绩</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="grade : ${grades}">
                        <td th:text="${grade.student_number ?: '未知'}"></td>
                        <td th:text="${grade.student_name ?: '未知'}"></td>
                        <td th:text="${grade.course_name ?: '未知'}"></td>
                        <td th:text="${grade.academic_year ?: '2024-2025'}"></td>
                        <td th:text="${grade.semester ?: '1'}"></td>
                        <td th:text="${grade.score != null and grade.score != '' ? grade.score : '未录入'}"></td>
                        <td>
                            <button class="edit-btn" onclick="editGrade(this)" th:data-grade-id="${grade.id ?: '0'}"
                                th:data-current-score="${grade.score != null and grade.score != '' ? grade.score : ''}"
                                th:data-academic-year="${grade.academic_year ?: '2024-2025'}"
                                th:data-semester="${grade.semester ?: '1'}"
                                th:data-student-id="${grade.student_id ?: '0'}"
                                th:data-course-id="${grade.course_id ?: '0'}">修改</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div th:if="${#lists.isEmpty(grades)}" style="text-align: center; padding: 50px; color: #666;">
                暂无成绩记录
            </div>
        </div>
    </div>

    <!-- 修改成绩模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>修改成绩</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editGradeForm" method="post" action="/teacher/update-grade">
                    <input type="hidden" id="gradeId" name="gradeId">
                    <input type="hidden" id="academicYear" name="academicYear">
                    <input type="hidden" id="semester" name="semester">
                    <input type="hidden" id="studentId" name="studentId">
                    <input type="hidden" id="courseId" name="courseId">
                    <div class="form-group">
                        <label for="score">成绩：</label>
                        <input type="number" id="score" name="score" min="0" max="100" step="0.1" required
                            placeholder="请输入0-100的成绩">
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="closeModal()" class="btn-cancel">取消</button>
                        <button type="submit" class="btn-submit">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function searchTable() {
            var studentFilter = document.getElementById("searchStudent").value.toUpperCase();
            var numberFilter = document.getElementById("searchNumber").value.toUpperCase();
            var table = document.getElementById("gradesTable");
            var tr = table.getElementsByTagName("tr");

            for (var i = 1; i < tr.length; i++) {
                var studentNumber = tr[i].getElementsByTagName("td")[0]; // 学号
                var studentName = tr[i].getElementsByTagName("td")[1];   // 学生姓名

                if (studentName && studentNumber) {
                    var studentText = studentName.textContent || studentName.innerText;
                    var numberText = studentNumber.textContent || studentNumber.innerText;

                    if (studentText.toUpperCase().indexOf(studentFilter) > -1 &&
                        numberText.toUpperCase().indexOf(numberFilter) > -1) {
                        tr[i].style.display = "";
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            }
        }

        function editGrade(btn) {
            console.log('=== 教师端成绩修改调试信息 ===');

            var gradeId = btn.getAttribute('data-grade-id');
            var currentScore = btn.getAttribute('data-current-score');
            var academicYear = btn.getAttribute('data-academic-year');
            var semester = btn.getAttribute('data-semester');
            var studentId = btn.getAttribute('data-student-id');
            var courseId = btn.getAttribute('data-course-id');

            console.log('gradeId:', gradeId);
            console.log('currentScore:', currentScore);
            console.log('academicYear:', academicYear);
            console.log('semester:', semester);
            console.log('studentId:', studentId);
            console.log('courseId:', courseId);

            // 填充表单数据
            document.getElementById('gradeId').value = gradeId;
            document.getElementById('score').value = currentScore;
            document.getElementById('academicYear').value = academicYear;
            document.getElementById('semester').value = semester;
            document.getElementById('studentId').value = studentId;
            document.getElementById('courseId').value = courseId;

            console.log('表单数据已填充');

            // 显示模态框
            document.getElementById('editModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function (event) {
            var modal = document.getElementById('editModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // 为表单添加提交事件监听器
        document.addEventListener('DOMContentLoaded', function () {
            var form = document.getElementById('editGradeForm');
            if (form) {
                form.addEventListener('submit', function (e) {
                    console.log('=== 表单提交调试信息 ===');
                    console.log('表单数据:');
                    var formData = new FormData(form);
                    for (var pair of formData.entries()) {
                        console.log(pair[0] + ': ' + pair[1]);
                    }
                    console.log('表单提交到:', form.action);
                    console.log('提交方法:', form.method);
                });
            }
        });
    </script>
</body>

</html>