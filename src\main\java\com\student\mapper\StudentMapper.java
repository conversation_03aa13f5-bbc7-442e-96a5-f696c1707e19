package com.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.student.entity.Student;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 学生Mapper接口
 */
@Mapper
public interface StudentMapper extends BaseMapper<Student> {
    
    /**
     * 根据用户ID查找学生
     */
    @Select("SELECT * FROM students WHERE user_id = #{userId}")
    Student findByUserId(Long userId);

    /**
     * 根据班级ID统计学生人数
     */
    @Select("SELECT COUNT(*) FROM students WHERE class_id = #{classId}")
    int countByClassId(Long classId);

    /**
     * 根据班级ID查找学生列表
     */
    @Select("SELECT * FROM students WHERE class_id = #{classId} ORDER BY student_number")
    List<Student> findByClassId(Long classId);

    /**
     * 查找未分配班级的学生
     */
    @Select("SELECT * FROM students WHERE class_id IS NULL ORDER BY student_number")
    List<Student> findUnassignedStudents();
}
