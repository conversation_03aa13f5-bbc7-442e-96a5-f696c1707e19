package com.student;

import com.student.entity.User;
import com.student.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class GradeSystemApplicationTests {

    @Autowired
    private UserService userService;

    @Test
    void contextLoads() {
        // 测试Spring上下文是否能正常加载
    }

    @Test
    void testUserService() {
        // 测试用户服务是否正常工作
        // 注意：这个测试需要数据库连接
        try {
            User user = userService.findByUsername("admin");
            System.out.println("找到用户: " + (user != null ? user.getRealName() : "未找到"));
        } catch (Exception e) {
            System.out.println("数据库连接测试失败: " + e.getMessage());
        }
    }
}
