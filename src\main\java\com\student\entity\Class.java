package com.student.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 班级实体类
 */
@TableName("classes")
public class Class {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("class_name")
    private String className;
    
    @TableField("class_code")
    private String classCode;
    
    @TableField("grade_year")
    private Integer gradeYear;
    
    private String major;
    
    @TableField("teacher_id")
    private Long teacherId;
    
    // 构造函数
    public Class() {}
    
    public Class(String className, String classCode, Integer gradeYear, String major, Long teacherId) {
        this.className = className;
        this.classCode = classCode;
        this.gradeYear = gradeYear;
        this.major = major;
        this.teacherId = teacherId;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getClassName() {
        return className;
    }
    
    public void setClassName(String className) {
        this.className = className;
    }
    
    public String getClassCode() {
        return classCode;
    }
    
    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }
    
    public Integer getGradeYear() {
        return gradeYear;
    }
    
    public void setGradeYear(Integer gradeYear) {
        this.gradeYear = gradeYear;
    }
    
    public String getMajor() {
        return major;
    }
    
    public void setMajor(String major) {
        this.major = major;
    }
    
    public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    // 兼容性方法
    public Long getAdvisorTeacherId() {
        return this.teacherId;
    }

    public void setAdvisorTeacherId(Long advisorTeacherId) {
        this.teacherId = advisorTeacherId;
    }
}
