package com.student.controller;

import com.student.entity.Course;
import com.student.entity.Grade;
import com.student.entity.Student;
import com.student.entity.User;
import com.student.entity.Teacher;
import com.student.entity.Class;
import com.student.service.CourseService;
import com.student.service.GradeService;
import com.student.service.StudentService;
import com.student.service.UserService;
import com.student.service.ClassService;
import com.student.service.TeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员控制器
 */
@Controller
@RequestMapping("/admin")
public class AdminController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private StudentService studentService;
    
    @Autowired
    private CourseService courseService;
    
    @Autowired
    private GradeService gradeService;

    @Autowired
    private ClassService classService;

    @Autowired
    private TeacherService teacherService;
    
    /**
     * 管理员主页
     */
    @GetMapping("/dashboard")
    public ModelAndView dashboard(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }
        
        ModelAndView mv = new ModelAndView("admin/dashboard");
        mv.addObject("user", user);
        return mv;
    }
    
    /**
     * 用户管理
     */
    @GetMapping("/users")
    public ModelAndView manageUsers(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        List<User> users = userService.list();
        List<Class> classes = classService.getAllClasses();

        ModelAndView mv = new ModelAndView("admin/users");
        mv.addObject("user", user);
        mv.addObject("users", users);
        mv.addObject("classes", classes);
        return mv;
    }

    /**
     * 获取用户详情数据（查看）
     */
    @GetMapping("/users/view/{userId}/data")
    @ResponseBody
    public Map<String, Object> getUserDetailsData(@PathVariable Long userId, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        try {
            User user = (User) session.getAttribute("user");
            if (user == null || !"ADMIN".equals(user.getRole())) {
                response.put("success", false);
                response.put("message", "权限不足");
                return response;
            }

            User targetUser = userService.getById(userId);
            if (targetUser == null) {
                response.put("success", false);
                response.put("message", "用户不存在");
                return response;
            }

            Map<String, Object> userDetails = new HashMap<>();
            userDetails.put("user", targetUser);

            // 根据身份获取详细信息
            if ("STUDENT".equals(targetUser.getRole())) {
                Student student = studentService.getByUserId(userId);
                userDetails.put("student", student);
                if (student != null && student.getClassId() != null) {
                    Class studentClass = classService.getById(student.getClassId());
                    if (studentClass != null) {
                        userDetails.put("class", studentClass);
                        userDetails.put("className", studentClass.getClassName());
                        userDetails.put("major", studentClass.getMajor());
                        userDetails.put("gradeYear", studentClass.getGradeYear());
                    }
                }
            } else if ("TEACHER".equals(targetUser.getRole())) {
                Teacher teacher = teacherService.getByUserId(userId);
                userDetails.put("teacher", teacher);
            }

            response.put("success", true);
            response.put("userDetails", userDetails);
            return response;
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取用户信息失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 添加用户页面
     */
    @GetMapping("/users/add")
    public ModelAndView addUserPage(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        List<Class> classes = classService.getAllClasses();
        ModelAndView mv = new ModelAndView("admin/add-user");
        mv.addObject("user", user);
        mv.addObject("classes", classes);
        return mv;
    }

    /**
     * 添加用户处理
     */
    @PostMapping("/users/add")
    public ModelAndView addUser(
            @RequestParam String username,
            @RequestParam String password,
            @RequestParam String role,
            @RequestParam String realName,
            @RequestParam(required = false) String studentNumber,
            @RequestParam(required = false) Long classId,
            @RequestParam(required = false) String teacherNumber,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String department,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 检查用户名是否已存在
        User existingUser = userService.findByUsername(username);
        if (existingUser != null) {
            List<Class> classes = classService.getAllClasses();
            ModelAndView mv = new ModelAndView("admin/add-user");
            mv.addObject("user", user);
            mv.addObject("classes", classes);
            mv.addObject("error", "用户名已存在");
            return mv;
        }

        // 创建新用户
        User newUser = new User(username, password, role, realName);
        boolean success = userService.save(newUser);

        if (success) {
            // 根据身份创建对应的详细信息
            if ("STUDENT".equals(role) && studentNumber != null && classId != null) {
                Student student = new Student();
                student.setUserId(newUser.getId());
                student.setStudentNumber(studentNumber);
                student.setStudentName(realName);
                student.setClassId(classId);
                boolean studentSaved = studentService.save(student);

                // 学生创建成功，不再自动为学生创建成绩记录
                // 成绩记录将在教师实际录入成绩时创建
            } else if ("TEACHER".equals(role) && teacherNumber != null) {
                Teacher teacher = new Teacher();
                teacher.setUserId(newUser.getId());
                teacher.setTeacherNumber(teacherNumber);
                teacher.setTeacherName(realName);
                teacher.setPhone(phone);
                teacher.setEmail(email);
                teacher.setDepartment(department);
                teacherService.save(teacher);
            }
            return new ModelAndView("redirect:/admin/users");
        } else {
            List<Class> classes = classService.getAllClasses();
            ModelAndView mv = new ModelAndView("admin/add-user");
            mv.addObject("user", user);
            mv.addObject("classes", classes);
            mv.addObject("error", "用户创建失败");
            return mv;
        }
    }

    /**
     * 编辑用户页面
     */
    @GetMapping("/users/edit/{userId}")
    public ModelAndView editUserPage(@PathVariable Long userId, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        User targetUser = userService.getById(userId);
        if (targetUser == null) {
            return new ModelAndView("error", "message", "用户不存在");
        }

        Map<String, Object> editData = new HashMap<>();
        editData.put("targetUser", targetUser);

        // 获取所有班级
        List<Class> classes = classService.getAllClasses();
        editData.put("classes", classes);

        // 根据身份获取详细信息
        if ("STUDENT".equals(targetUser.getRole())) {
            Student student = studentService.getByUserId(userId);
            editData.put("student", student);
        } else if ("TEACHER".equals(targetUser.getRole())) {
            Teacher teacher = teacherService.getByUserId(userId);
            editData.put("teacher", teacher);
            if (teacher != null) {
                editData.put("phone", teacher.getPhone());
                editData.put("email", teacher.getEmail());
                editData.put("department", teacher.getDepartment());
            }
        }

        ModelAndView mv = new ModelAndView("admin/edit-user");
        mv.addObject("user", user);
        mv.addObject("editData", editData);
        return mv;
    }

    /**
     * 更新用户信息
     */
    @PostMapping("/users/update")
    public ModelAndView updateUser(
            @RequestParam Long userId,
            @RequestParam String username,
            @RequestParam String realName,
            @RequestParam(required = false) String password,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String department,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        try {
            User targetUser = userService.getById(userId);
            if (targetUser == null) {
                return new ModelAndView("redirect:/admin/users?error=" + URLEncoder.encode("用户不存在", StandardCharsets.UTF_8));
            }

            // 更新用户基本信息
            targetUser.setUsername(username);
            targetUser.setRealName(realName);
            if (password != null && !password.trim().isEmpty()) {
                targetUser.setPassword(password);
            }

            boolean userUpdateSuccess = userService.updateById(targetUser);
            if (!userUpdateSuccess) {
                return new ModelAndView("redirect:/admin/users?error=" + URLEncoder.encode("用户基本信息更新失败", StandardCharsets.UTF_8));
            }

            // 根据身份更新详细信息
            if ("TEACHER".equals(targetUser.getRole())) {
                Teacher teacher = teacherService.getByUserId(userId);
                if (teacher != null) {
                    boolean teacherUpdated = false;

                    // 同步更新教师姓名
                    if (!realName.equals(teacher.getTeacherName())) {
                        teacher.setTeacherName(realName);
                        teacherUpdated = true;
                    }

                    if (phone != null && !phone.trim().isEmpty()) {
                        teacher.setPhone(phone);
                        teacherUpdated = true;
                    }
                    if (email != null && !email.trim().isEmpty()) {
                        teacher.setEmail(email);
                        teacherUpdated = true;
                    }
                    if (department != null && !department.trim().isEmpty()) {
                        teacher.setDepartment(department);
                        teacherUpdated = true;
                    }

                    if (teacherUpdated) {
                        // 使用手动更新避免MyBatis-Plus的自动字段问题
                        boolean teacherUpdateSuccess = teacherService.updateTeacherInfo(teacher);
                        if (!teacherUpdateSuccess) {
                            String errorMsg = URLEncoder.encode("教师信息更新失败", StandardCharsets.UTF_8);
                            return new ModelAndView("redirect:/admin/users?error=" + errorMsg);
                        }
                    }
                }
            } else if ("STUDENT".equals(targetUser.getRole())) {
                // 同步更新学生姓名
                Student student = studentService.getByUserId(userId);
                if (student != null && !realName.equals(student.getStudentName())) {
                    student.setStudentName(realName);
                    boolean studentUpdateSuccess = studentService.updateById(student);
                    if (!studentUpdateSuccess) {
                        String errorMsg = URLEncoder.encode("学生信息更新失败", StandardCharsets.UTF_8);
                        return new ModelAndView("redirect:/admin/users?error=" + errorMsg);
                    }
                }
            }

            return new ModelAndView("redirect:/admin/users");
        } catch (Exception e) {
            e.printStackTrace(); // 添加错误日志
            String errorMsg = URLEncoder.encode("用户信息更新失败：" + e.getMessage(), StandardCharsets.UTF_8);
            return new ModelAndView("redirect:/admin/users?error=" + errorMsg);
        }
    }

    /**
     * 删除用户
     */
    @PostMapping("/users/delete/{userId}")
    public ModelAndView deleteUser(@PathVariable Long userId, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 不能删除自己
        if (userId.equals(user.getId())) {
            String errorMsg = URLEncoder.encode("不能删除自己", StandardCharsets.UTF_8);
            return new ModelAndView("redirect:/admin/users?error=" + errorMsg);
        }

        try {
            // 获取要删除的用户信息
            User targetUser = userService.getById(userId);
            if (targetUser == null) {
                String errorMsg = URLEncoder.encode("用户不存在", StandardCharsets.UTF_8);
                return new ModelAndView("redirect:/admin/users?error=" + errorMsg);
            }

            // 根据用户角色进行级联删除
            if ("STUDENT".equals(targetUser.getRole())) {
                // 删除学生相关数据
                Student student = studentService.getByUserId(userId);
                if (student != null) {
                    // 删除学生的成绩记录
                    gradeService.deleteGradesByStudentId(student.getId());
                    // 删除学生记录
                    studentService.removeById(student.getId());
                }
            } else if ("TEACHER".equals(targetUser.getRole())) {
                // 删除教师相关数据
                Teacher teacher = teacherService.getByUserId(userId);
                if (teacher != null) {
                    // 清空该教师作为班主任的班级
                    classService.clearTeacherFromClasses(teacher.getId());
                    // 清空该教师的课程
                    courseService.clearTeacherFromCourses(teacher.getId());
                    // 删除教师记录
                    teacherService.removeById(teacher.getId());
                }
            }

            // 删除用户记录
            userService.removeById(userId);
            return new ModelAndView("redirect:/admin/users");
        } catch (Exception e) {
            String errorMsg = URLEncoder.encode("删除失败：" + e.getMessage(), StandardCharsets.UTF_8);
            return new ModelAndView("redirect:/admin/users?error=" + errorMsg);
        }
    }

    /**
     * 班级管理
     */
    @GetMapping("/classes")
    public ModelAndView manageClasses(HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        List<Class> classes = classService.getAllClasses();
        List<Teacher> teachers = teacherService.list();

        // 为每个班级添加班主任姓名和学生人数
        List<Map<String, Object>> classesWithDetails = new ArrayList<>();
        for (Class clazz : classes) {
            Map<String, Object> classDetail = new HashMap<>();
            classDetail.put("id", clazz.getId());
            classDetail.put("className", clazz.getClassName());
            classDetail.put("classCode", clazz.getClassCode());
            classDetail.put("gradeYear", clazz.getGradeYear());
            classDetail.put("major", clazz.getMajor());
            classDetail.put("teacherId", clazz.getTeacherId());

            // 获取班主任姓名（通过teachers表）
            String teacherName = "未分配";
            if (clazz.getTeacherId() != null) {
                Teacher teacher = teacherService.getById(clazz.getTeacherId());
                if (teacher != null) {
                    teacherName = teacher.getTeacherName();
                } else {
                    // 如果班主任记录不存在，清空班主任ID
                    clazz.setTeacherId(null);
                    classService.updateById(clazz);
                    teacherName = "未分配";
                }
            }
            classDetail.put("teacherName", teacherName);

            // 获取学生人数
            int studentCount = studentService.getStudentCountByClassId(clazz.getId());
            classDetail.put("studentCount", studentCount);

            classesWithDetails.add(classDetail);
        }

        ModelAndView mv = new ModelAndView("admin/classes");
        mv.addObject("user", user);
        mv.addObject("classes", classesWithDetails);
        mv.addObject("teachers", teachers);
        return mv;
    }

    /**
     * 获取班级详情数据（AJAX接口）
     */
    @GetMapping("/classes/view/{classId}/data")
    @ResponseBody
    public Map<String, Object> getClassDetailsData(@PathVariable Long classId, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        try {
            User user = (User) session.getAttribute("user");
            if (user == null || !"ADMIN".equals(user.getRole())) {
                response.put("success", false);
                response.put("message", "权限不足");
                return response;
            }

            Class targetClass = classService.getById(classId);
            if (targetClass == null) {
                response.put("success", false);
                response.put("message", "班级不存在");
                return response;
            }

            Map<String, Object> classDetails = new HashMap<>();
            classDetails.put("class", targetClass);

            // 获取班主任信息（通过teachers表）
            if (targetClass.getTeacherId() != null) {
                Teacher teacher = teacherService.getById(targetClass.getTeacherId());
                if (teacher != null) {
                    classDetails.put("teacherName", teacher.getTeacherName());
                } else {
                    // 如果班主任记录不存在，清空班主任ID
                    targetClass.setTeacherId(null);
                    classService.updateById(targetClass);
                    classDetails.put("teacherName", "未分配");
                }
            } else {
                classDetails.put("teacherName", "未分配");
            }

            // 获取学生人数
            int studentCount = studentService.getStudentCountByClassId(classId);
            classDetails.put("studentCount", studentCount);

            response.put("success", true);
            response.put("classDetails", classDetails);
            return response;
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取班级信息失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 添加班级页面
     */
    @GetMapping("/classes/add")
    public ModelAndView addClassPage(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 获取所有教师用于选择班主任
        List<Teacher> teachers = teacherService.list();

        ModelAndView mv = new ModelAndView("admin/add-class");
        mv.addObject("user", user);
        mv.addObject("teachers", teachers);
        return mv;
    }

    /**
     * 添加班级处理
     */
    @PostMapping("/classes/add")
    public ModelAndView addClass(
            @RequestParam String className,
            @RequestParam String classCode,
            @RequestParam Integer gradeYear,
            @RequestParam String major,
            @RequestParam(required = false) Long teacherId,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 创建新班级
        Class newClass = new Class(className, classCode, gradeYear, major, teacherId);
        boolean success = classService.createClass(newClass);

        if (success) {
            return new ModelAndView("redirect:/admin/classes");
        } else {
            List<Teacher> teachers = teacherService.list();
            ModelAndView mv = new ModelAndView("admin/add-class");
            mv.addObject("user", user);
            mv.addObject("teachers", teachers);
            mv.addObject("error", "班级创建失败");
            return mv;
        }
    }

    /**
     * 编辑班级页面
     */
    @GetMapping("/classes/edit/{classId}")
    public ModelAndView editClassPage(@PathVariable Long classId, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        Class targetClass = classService.getById(classId);
        if (targetClass == null) {
            return new ModelAndView("error", "message", "班级不存在");
        }

        List<Teacher> teachers = teacherService.list();

        ModelAndView mv = new ModelAndView("admin/edit-class");
        mv.addObject("user", user);
        mv.addObject("targetClass", targetClass);
        mv.addObject("teachers", teachers);
        return mv;
    }

    /**
     * 更新班级信息
     */
    @PostMapping("/classes/update")
    public ModelAndView updateClass(
            @RequestParam Long classId,
            @RequestParam String className,
            @RequestParam String classCode,
            @RequestParam Integer gradeYear,
            @RequestParam String major,
            @RequestParam(required = false) Long teacherId,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        Class targetClass = classService.getById(classId);
        if (targetClass != null) {
            targetClass.setClassName(className);
            targetClass.setClassCode(classCode);
            targetClass.setGradeYear(gradeYear);
            targetClass.setMajor(major);
            targetClass.setTeacherId(teacherId);
            classService.updateClass(targetClass);
        }

        return new ModelAndView("redirect:/admin/classes");
    }

    /**
     * 删除班级
     */
    @PostMapping("/classes/delete/{classId}")
    public ModelAndView deleteClass(@PathVariable Long classId, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        try {
            classService.deleteClass(classId);
            String successMsg = URLEncoder.encode("删除成功", StandardCharsets.UTF_8);
            return new ModelAndView("redirect:/admin/classes?message=" + successMsg);
        } catch (Exception e) {
            String errorMsg = URLEncoder.encode("删除失败：" + e.getMessage(), StandardCharsets.UTF_8);
            return new ModelAndView("redirect:/admin/classes?error=" + errorMsg);
        }
    }

    /**
     * 获取班级学生分配页面数据（AJAX接口）
     */
    @GetMapping("/classes/{classId}/students/data")
    @ResponseBody
    public Map<String, Object> getClassStudentsData(@PathVariable Long classId, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        try {
            User user = (User) session.getAttribute("user");
            if (user == null || !"ADMIN".equals(user.getRole())) {
                response.put("success", false);
                response.put("message", "权限不足");
                return response;
            }

            Class targetClass = classService.getById(classId);
            if (targetClass == null) {
                response.put("success", false);
                response.put("message", "班级不存在");
                return response;
            }

            // 获取班级中的学生
            List<Student> classStudents = studentService.getStudentsByClassId(classId);

            // 获取所有未分配班级的学生
            List<Student> unassignedStudents = studentService.getUnassignedStudents();

            response.put("success", true);
            response.put("className", targetClass.getClassName());
            response.put("classStudents", classStudents);
            response.put("unassignedStudents", unassignedStudents);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取数据失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 分配学生到班级（AJAX接口）
     */
    @PostMapping("/classes/{classId}/students/assign")
    @ResponseBody
    public Map<String, Object> assignStudentsToClass(
            @PathVariable Long classId,
            @RequestBody Map<String, Object> request,
            HttpSession session) {

        Map<String, Object> response = new HashMap<>();

        try {
            User user = (User) session.getAttribute("user");
            if (user == null || !"ADMIN".equals(user.getRole())) {
                response.put("success", false);
                response.put("message", "权限不足");
                return response;
            }

            Class targetClass = classService.getById(classId);
            if (targetClass == null) {
                response.put("success", false);
                response.put("message", "班级不存在");
                return response;
            }

            @SuppressWarnings("unchecked")
            List<Long> studentIds = (List<Long>) request.get("studentIds");

            if (studentIds == null || studentIds.isEmpty()) {
                response.put("success", false);
                response.put("message", "请选择要分配的学生");
                return response;
            }

            // 批量更新学生的班级ID
            for (Long studentId : studentIds) {
                Student student = studentService.getById(studentId);
                if (student != null) {
                    student.setClassId(classId);
                    studentService.updateById(student);
                }
            }

            response.put("success", true);
            response.put("message", "学生分配成功");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "分配失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 从班级中移除学生（AJAX接口）
     */
    @PostMapping("/classes/{classId}/students/remove")
    @ResponseBody
    public Map<String, Object> removeStudentsFromClass(
            @PathVariable Long classId,
            @RequestBody Map<String, Object> request,
            HttpSession session) {

        Map<String, Object> response = new HashMap<>();

        try {
            User user = (User) session.getAttribute("user");
            if (user == null || !"ADMIN".equals(user.getRole())) {
                response.put("success", false);
                response.put("message", "权限不足");
                return response;
            }

            @SuppressWarnings("unchecked")
            List<Long> studentIds = (List<Long>) request.get("studentIds");

            if (studentIds == null || studentIds.isEmpty()) {
                response.put("success", false);
                response.put("message", "请选择要移除的学生");
                return response;
            }

            // 批量清空学生的班级ID
            for (Long studentId : studentIds) {
                Student student = studentService.getById(studentId);
                if (student != null && classId.equals(student.getClassId())) {
                    student.setClassId(null);
                    studentService.updateById(student);
                }
            }

            response.put("success", true);
            response.put("message", "学生移除成功");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "移除失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取所有学生数据（AJAX接口）
     */
    @GetMapping("/students/all")
    @ResponseBody
    public Map<String, Object> getAllStudents(HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        try {
            User user = (User) session.getAttribute("user");
            if (user == null || !"ADMIN".equals(user.getRole())) {
                response.put("success", false);
                response.put("message", "权限不足");
                return response;
            }

            List<Student> students = studentService.list();
            List<Map<String, Object>> studentsWithClass = new ArrayList<>();

            for (Student student : students) {
                Map<String, Object> studentData = new HashMap<>();
                studentData.put("id", student.getId());
                studentData.put("studentName", student.getStudentName());
                studentData.put("studentNumber", student.getStudentNumber());
                studentData.put("classId", student.getClassId());

                // 获取班级名称
                if (student.getClassId() != null) {
                    Class clazz = classService.getById(student.getClassId());
                    if (clazz != null) {
                        studentData.put("className", clazz.getClassName());
                    } else {
                        studentData.put("className", "班级不存在");
                    }
                } else {
                    studentData.put("className", null);
                }

                studentsWithClass.add(studentData);
            }

            response.put("success", true);
            response.put("students", studentsWithClass);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取学生数据失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取所有班级数据（AJAX接口）
     */
    @GetMapping("/classes/all")
    @ResponseBody
    public Map<String, Object> getAllClasses(HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        try {
            User user = (User) session.getAttribute("user");
            if (user == null || !"ADMIN".equals(user.getRole())) {
                response.put("success", false);
                response.put("message", "权限不足");
                return response;
            }

            List<Class> classes = classService.getAllClasses();
            response.put("success", true);
            response.put("classes", classes);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取班级数据失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 分配学生到班级（AJAX接口）
     */
    @PostMapping("/students/{studentId}/assign-class")
    @ResponseBody
    public Map<String, Object> assignStudentToClass(
            @PathVariable Long studentId,
            @RequestBody Map<String, Object> request,
            HttpSession session) {

        Map<String, Object> response = new HashMap<>();

        try {
            User user = (User) session.getAttribute("user");
            if (user == null || !"ADMIN".equals(user.getRole())) {
                response.put("success", false);
                response.put("message", "权限不足");
                return response;
            }

            Student student = studentService.getById(studentId);
            if (student == null) {
                response.put("success", false);
                response.put("message", "学生不存在");
                return response;
            }

            Object classIdObj = request.get("classId");
            Long classId = null;
            if (classIdObj != null && !classIdObj.toString().isEmpty()) {
                classId = Long.valueOf(classIdObj.toString());

                // 验证班级是否存在
                Class targetClass = classService.getById(classId);
                if (targetClass == null) {
                    response.put("success", false);
                    response.put("message", "班级不存在");
                    return response;
                }
            }

            student.setClassId(classId);
            boolean success = studentService.updateById(student);

            response.put("success", success);
            if (success) {
                if (classId != null) {
                    Class targetClass = classService.getById(classId);
                    response.put("message", "学生已分配到班级：" + targetClass.getClassName());
                } else {
                    response.put("message", "学生已设为未分配状态");
                }
            } else {
                response.put("message", "分配失败");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "分配失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 课程管理
     */
    @GetMapping("/courses")
    public ModelAndView manageCourses(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        List<Course> courses = courseService.list();
        List<Teacher> teachers = teacherService.list();

        // 为每个课程添加教师姓名
        List<Map<String, Object>> coursesWithDetails = new ArrayList<>();
        for (Course course : courses) {
            Map<String, Object> courseDetail = new HashMap<>();
            courseDetail.put("id", course.getId());
            courseDetail.put("courseName", course.getCourseName());
            courseDetail.put("courseCode", course.getCourseCode());
            courseDetail.put("credits", course.getCredits());
            courseDetail.put("teachingHours", course.getTeachingHours());
            courseDetail.put("classroom", course.getClassroom());
            courseDetail.put("teacherId", course.getTeacherId());

            // 获取教师姓名
            String teacherName = "未分配";
            if (course.getTeacherId() != null) {
                Teacher teacher = teacherService.getById(course.getTeacherId());
                if (teacher != null) {
                    teacherName = teacher.getTeacherName();
                }
            }
            courseDetail.put("teacherName", teacherName);

            coursesWithDetails.add(courseDetail);
        }

        ModelAndView mv = new ModelAndView("admin/courses");
        mv.addObject("user", user);
        mv.addObject("courses", coursesWithDetails);
        mv.addObject("teachers", teachers);
        return mv;
    }

    /**
     * 添加课程页面
     */
    @GetMapping("/courses/add")
    public ModelAndView addCoursePage(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        List<Teacher> teachers = teacherService.list();

        ModelAndView mv = new ModelAndView("admin/add-course");
        mv.addObject("user", user);
        mv.addObject("teachers", teachers);
        return mv;
    }

    /**
     * 添加课程处理
     */
    @PostMapping("/courses/add")
    public ModelAndView addCourse(
            @RequestParam String courseName,
            @RequestParam String courseCode,
            @RequestParam Integer credits,
            @RequestParam Integer teachingHours,
            @RequestParam(required = false) Long teacherId,
            @RequestParam String classroom,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        Course course = new Course();
        course.setCourseName(courseName);
        course.setCourseCode(courseCode);
        course.setCredits(credits);
        course.setTeachingHours(teachingHours);
        course.setTeacherId(teacherId);
        course.setClassroom(classroom);

        boolean success = courseService.save(course);

        if (success) {
            // 课程创建成功，不再自动为学生创建成绩记录
            // 成绩记录将在教师实际录入成绩时创建
            String successMsg = URLEncoder.encode("课程创建成功", StandardCharsets.UTF_8);
            return new ModelAndView("redirect:/admin/courses?message=" + successMsg);
        } else {
            List<Teacher> teachers = teacherService.list();
            ModelAndView mv = new ModelAndView("admin/add-course");
            mv.addObject("user", user);
            mv.addObject("teachers", teachers);
            mv.addObject("error", "课程创建失败");
            return mv;
        }
    }
    
    /**
     * 编辑课程页面
     */
    @GetMapping("/courses/edit/{courseId}")
    public ModelAndView editCoursePage(@PathVariable Long courseId, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        Course course = courseService.getById(courseId);
        if (course == null) {
            return new ModelAndView("error", "message", "课程不存在");
        }

        List<Teacher> teachers = teacherService.list();

        ModelAndView mv = new ModelAndView("admin/edit-course");
        mv.addObject("user", user);
        mv.addObject("course", course);
        mv.addObject("teachers", teachers);
        return mv;
    }

    /**
     * 更新课程信息
     */
    @PostMapping("/courses/update")
    public ModelAndView updateCourse(
            @RequestParam Long courseId,
            @RequestParam String courseName,
            @RequestParam String courseCode,
            @RequestParam Integer credits,
            @RequestParam Integer teachingHours,
            @RequestParam(required = false) Long teacherId,
            @RequestParam String classroom,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        Course course = courseService.getById(courseId);
        if (course != null) {
            course.setCourseName(courseName);
            course.setCourseCode(courseCode);
            course.setCredits(credits);
            course.setTeachingHours(teachingHours);
            course.setTeacherId(teacherId);
            course.setClassroom(classroom);
            courseService.updateById(course);
        }

        return new ModelAndView("redirect:/admin/courses");
    }

    /**
     * 删除课程
     */
    @PostMapping("/courses/delete/{courseId}")
    public ModelAndView deleteCourse(@PathVariable Long courseId, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        try {
            // 使用安全删除方法
            CourseService.CourseDeleteResult result = courseService.safeDeleteCourse(courseId);

            if (result.isSuccess()) {
                String successMsg = URLEncoder.encode(result.getMessage(), StandardCharsets.UTF_8);
                return new ModelAndView("redirect:/admin/courses?message=" + successMsg);
            } else {
                String errorMsg = URLEncoder.encode(result.getMessage(), StandardCharsets.UTF_8);
                return new ModelAndView("redirect:/admin/courses?error=" + errorMsg);
            }
        } catch (Exception e) {
            String errorMsg = URLEncoder.encode("删除失败：" + e.getMessage(), StandardCharsets.UTF_8);
            return new ModelAndView("redirect:/admin/courses?error=" + errorMsg);
        }
    }

    /**
     * 课程班级分配页面数据（AJAX接口）
     */
    @GetMapping("/courses/{courseId}/classes/data")
    @ResponseBody
    public Map<String, Object> getCourseClassesData(@PathVariable Long courseId, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        try {
            User user = (User) session.getAttribute("user");
            if (user == null || !"ADMIN".equals(user.getRole())) {
                response.put("success", false);
                response.put("message", "权限不足");
                return response;
            }

            Course course = courseService.getById(courseId);
            if (course == null) {
                response.put("success", false);
                response.put("message", "课程不存在");
                return response;
            }

            // 获取所有班级
            List<Class> allClasses = classService.getAllClasses();

            // 获取已分配给该课程的班级（这里简化处理，实际应该查询course_classes表）
            // 由于当前数据库设计限制，我们暂时返回所有班级
            List<Teacher> teachers = teacherService.list();

            response.put("success", true);
            response.put("courseName", course.getCourseName());
            response.put("allClasses", allClasses);
            response.put("teachers", teachers);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取数据失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 成绩审核
     */
    @GetMapping("/grades")
    public ModelAndView reviewGrades(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        List<Map<String, Object>> grades = new ArrayList<>();
        List<Class> classes = new ArrayList<>();
        List<Course> courses = new ArrayList<>();
        String errorMessage = null;

        // 查询成绩数据
        try {
            grades = gradeService.findAllGradesWithDetails();
        } catch (Exception e) {
            errorMessage = "成绩数据查询失败: " + e.getMessage();
            grades = new ArrayList<>();
        }

        // 查询班级数据
        if (errorMessage == null) {
            try {
                classes = classService.getAllClasses();
            } catch (Exception e) {
                // 班级查询失败不影响主要功能
                classes = new ArrayList<>();
            }
        }

        // 查询课程数据
        if (errorMessage == null) {
            try {
                courses = courseService.list();
            } catch (Exception e) {
                // 课程查询失败不影响主要功能
                courses = new ArrayList<>();
            }
        }

        // 构建返回结果
        ModelAndView mv = new ModelAndView("admin/grades");
        mv.addObject("user", user);
        mv.addObject("grades", grades);
        mv.addObject("classes", classes);
        mv.addObject("courses", courses);

        if (errorMessage != null) {
            mv.addObject("error", errorMessage);
        }
        return mv;
    }

    /**
     * 更新成绩（AJAX接口）
     */
    @PostMapping("/grades/update/{gradeId}")
    @ResponseBody
    public Map<String, Object> updateGrade(
            @PathVariable Long gradeId,
            @RequestBody Map<String, Object> request,
            HttpSession session) {

        Map<String, Object> response = new HashMap<>();

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            response.put("success", false);
            response.put("message", "权限不足");
            return response;
        }

        try {
            Double newScore = Double.valueOf(request.get("score").toString());

            if (newScore < 0 || newScore > 100) {
                response.put("success", false);
                response.put("message", "成绩必须在0-100之间");
                return response;
            }

            Grade grade = gradeService.getById(gradeId);
            if (grade == null) {
                response.put("success", false);
                response.put("message", "成绩记录不存在");
                return response;
            }

            grade.setScore(BigDecimal.valueOf(newScore));
            boolean success = gradeService.updateById(grade);

            response.put("success", success);
            response.put("message", success ? "成绩更新成功" : "成绩更新失败");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新失败：" + e.getMessage());
        }

        return response;
    }

    /**
     * 标记成绩异常（AJAX接口）
     */
    @PostMapping("/grades/mark/{gradeId}")
    @ResponseBody
    public Map<String, Object> markGrade(
            @PathVariable Long gradeId,
            @RequestBody Map<String, Object> request,
            HttpSession session) {

        Map<String, Object> response = new HashMap<>();

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            response.put("success", false);
            response.put("message", "权限不足");
            return response;
        }

        try {
            String action = request.get("action").toString();
            Grade grade = gradeService.getById(gradeId);

            if (grade == null) {
                response.put("success", false);
                response.put("message", "成绩记录不存在");
                return response;
            }

            // 记录操作前的状态
            Integer oldStatus = grade.getIsAbnormal();

            // 根据操作类型设置异常标记
            if ("标记为异常".equals(action)) {
                // 只有正常成绩才能标记为异常
                if (oldStatus != null && oldStatus == 1) {
                    response.put("success", false);
                    response.put("message", "该成绩已经是异常状态");
                    return response;
                }
                grade.setIsAbnormal(1);
            } else if ("审核为正常".equals(action)) {
                // 只有异常成绩才能审核为正常
                if (oldStatus == null || oldStatus == 0) {
                    response.put("success", false);
                    response.put("message", "该成绩已经是正常状态");
                    return response;
                }
                grade.setIsAbnormal(0);
            } else {
                response.put("success", false);
                response.put("message", "未知操作类型: " + action);
                return response;
            }

            boolean success = gradeService.updateById(grade);

            if (success) {
                response.put("success", true);
                response.put("message", "操作成功");
            } else {
                response.put("success", false);
                response.put("message", "数据库更新失败");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "操作失败：" + e.getMessage());
        }

        return response;
    }

    /**
     * 获取成绩详情（AJAX接口）
     */
    @GetMapping("/grades/detail/{gradeId}")
    @ResponseBody
    public Map<String, Object> getGradeDetail(@PathVariable Long gradeId, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        User user = (User) session.getAttribute("user");
        if (user == null || !"ADMIN".equals(user.getRole())) {
            response.put("success", false);
            response.put("message", "权限不足");
            return response;
        }

        try {
            Grade grade = gradeService.getById(gradeId);
            if (grade == null) {
                response.put("success", false);
                response.put("message", "成绩记录不存在");
                return response;
            }

            // 获取学生和课程信息
            Student student = studentService.getById(grade.getStudentId());
            Course course = courseService.getById(grade.getCourseId());

            Map<String, Object> gradeDetail = new HashMap<>();
            gradeDetail.put("id", grade.getId());
            gradeDetail.put("score", grade.getScore());
            gradeDetail.put("academicYear", grade.getAcademicYear());
            gradeDetail.put("semester", grade.getSemester());
            gradeDetail.put("isAbnormal", grade.getIsAbnormal());

            if (student != null) {
                gradeDetail.put("studentName", student.getStudentName());
                gradeDetail.put("studentNumber", student.getStudentNumber());
            }

            if (course != null) {
                gradeDetail.put("courseName", course.getCourseName());
                gradeDetail.put("courseCode", course.getCourseCode());
            }

            response.put("success", true);
            response.put("grade", gradeDetail);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取成绩详情失败: " + e.getMessage());
        }

        return response;
    }










}
