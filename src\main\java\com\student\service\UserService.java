package com.student.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.student.entity.User;
import com.student.mapper.UserMapper;
import org.springframework.stereotype.Service;

/**
 * 用户服务类
 */
@Service
public class UserService extends ServiceImpl<UserMapper, User> {
    
    /**
     * 用户登录
     */
    public User login(String username, String password) {
        return baseMapper.findByUsernameAndPassword(username, password);
    }
    
    /**
     * 根据用户名查找用户
     */
    public User findByUsername(String username) {
        return baseMapper.findByUsername(username);
    }

    /**
     * 根据角色查询用户
     */
    public java.util.List<User> getUsersByRole(String role) {
        return baseMapper.findByRole(role);
    }

    /**
     * 搜索用户（支持姓名、用户名、角色筛选）
     */
    public java.util.List<User> searchUsers(String realName, String username, String role) {
        return baseMapper.searchUsers(realName, username, role);
    }
}
