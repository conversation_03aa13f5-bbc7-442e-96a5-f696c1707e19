<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .header {
            background-color: #dc3545;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .add-btn {
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .add-btn:hover {
            background-color: #218838;
        }

        .message {
            padding: 10px 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-weight: bold;
        }

        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/admin/dashboard" class="back-btn">← 返回主页</a>

            <h2>课程管理</h2>

            <!-- 消息显示区域 -->
            <div th:if="${param.message}" class="message success" th:text="${param.message}"></div>
            <div th:if="${param.error}" class="message error" th:text="${param.error}"></div>

            <a href="/admin/courses/add" class="add-btn" style="text-decoration: none;">+ 添加课程</a>

            <div class="search-box" style="margin: 20px 0;">
                <input type="text" id="searchCourse" placeholder="按课程名称搜索..." onkeyup="searchTable()"
                    style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px; width: 200px;">
                <input type="text" id="searchTeacher" placeholder="按教师姓名搜索..." onkeyup="searchTable()"
                    style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px;">
            </div>

            <table id="coursesTable">
                <thead>
                    <tr>
                        <th>课程编号</th>
                        <th>课程名称</th>
                        <th>课程代码</th>
                        <th>学分</th>
                        <th>学时</th>
                        <th>授课教师</th>
                        <th>教室</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="course : ${courses}">
                        <td th:text="${course.id}"></td>
                        <td th:text="${course.courseName}"></td>
                        <td th:text="${course.courseCode}"></td>
                        <td th:text="${course.credits}"></td>
                        <td th:text="${course.teachingHours}"></td>
                        <td th:text="${course.teacherName}"></td>
                        <td th:text="${course.classroom}"></td>
                        <td>
                            <a th:href="@{/admin/courses/edit/{id}(id=${course.id})}"
                                style="padding: 5px 10px; margin-right: 5px; background-color: #007bff; color: white; border: none; border-radius: 3px; text-decoration: none; font-size: 12px; display: inline-block; min-width: 40px; text-align: center;">编辑</a>
                            <button th:onclick="'deleteCourse(' + ${course.id} + ')'"
                                style="padding: 5px 10px; background-color: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px; min-width: 40px;">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div th:if="${#lists.isEmpty(courses)}" style="text-align: center; padding: 50px; color: #666;">
                暂无课程记录
            </div>
        </div>
    </div>



    <script>
        function searchTable() {
            var courseFilter = document.getElementById("searchCourse").value.toUpperCase();
            var teacherFilter = document.getElementById("searchTeacher").value.toUpperCase();
            var table = document.getElementById("coursesTable");
            var tr = table.getElementsByTagName("tr");

            for (var i = 1; i < tr.length; i++) {
                var courseName = tr[i].getElementsByTagName("td")[1];
                var teacherName = tr[i].getElementsByTagName("td")[5];

                if (courseName && teacherName) {
                    var courseText = courseName.textContent || courseName.innerText;
                    var teacherText = teacherName.textContent || teacherName.innerText;

                    if (courseText.toUpperCase().indexOf(courseFilter) > -1 &&
                        teacherText.toUpperCase().indexOf(teacherFilter) > -1) {
                        tr[i].style.display = "";
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            }
        }

        function deleteCourse(courseId) {
            if (confirm('是否确认删除？')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/courses/delete/' + courseId;
                document.body.appendChild(form);
                form.submit();
            }
        }


    </script>
</body>

</html>