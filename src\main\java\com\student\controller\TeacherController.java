package com.student.controller;

import com.student.entity.Grade;
import com.student.entity.User;
import com.student.entity.Teacher;
import com.student.entity.Course;
import com.student.service.GradeService;
import com.student.service.StudentService;
import com.student.service.UserService;
import com.student.service.CourseService;
import com.student.service.ClassService;
import com.student.service.TeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 教师控制器
 */
@Controller
@RequestMapping("/teacher")
public class TeacherController {

    @Autowired
    private GradeService gradeService;

    @Autowired
    private StudentService studentService;

    @Autowired
    private UserService userService;

    @Autowired
    private CourseService courseService;

    @Autowired
    private ClassService classService;

    @Autowired
    private TeacherService teacherService;

    /**
     * 教师主页
     */
    @GetMapping("/dashboard")
    public ModelAndView dashboard(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"TEACHER".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        ModelAndView mv = new ModelAndView("teacher/dashboard");
        mv.addObject("user", user);
        return mv;
    }

    /**
     * 成绩管理页面
     */
    @GetMapping("/grades")
    public ModelAndView manageGrades(
            @RequestParam(value = "message", required = false) String message,
            @RequestParam(value = "error", required = false) String error,
            HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"TEACHER".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 根据用户ID获取教师信息
        Teacher teacher = teacherService.getByUserId(user.getId());
        if (teacher == null) {
            return new ModelAndView("error", "message", "教师信息不存在");
        }

        // 获取该教师授课的课程列表
        List<Map<String, Object>> courses = gradeService.findCoursesByTeacherId(teacher.getId());

        // 直接显示该教师的所有课程成绩（因为每个老师只教一门课）
        List<Map<String, Object>> grades = null;
        if (!courses.isEmpty()) {
            // 获取第一门课程的ID（因为每个老师只教一门课）
            Long courseId = ((Number) courses.get(0).get("id")).longValue();
            grades = gradeService.findGradesByTeacherIdAndCourse(teacher.getId(), courseId);
        }

        ModelAndView mv = new ModelAndView("teacher/grades");
        mv.addObject("user", user);
        mv.addObject("grades", grades);

        // 添加消息到模型
        if (message != null && !message.trim().isEmpty()) {
            mv.addObject("message", message);
        }
        if (error != null && !error.trim().isEmpty()) {
            mv.addObject("error", error);
        }

        return mv;
    }

    /**
     * 录入/修改成绩
     */
    @PostMapping("/grades/save")
    public ModelAndView saveGrade(@RequestParam Long studentId,
                                  @RequestParam Long courseId,
                                  @RequestParam BigDecimal score,
                                  @RequestParam String academicYear,
                                  @RequestParam Integer semester,
                                  HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"TEACHER".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 创建或更新成绩
        Grade grade = new Grade();
        grade.setStudentId(studentId);
        grade.setCourseId(courseId);
        grade.setScore(score);
        grade.setAcademicYear(academicYear);
        grade.setSemester(semester);
        gradeService.save(grade);

        return new ModelAndView("redirect:/teacher/grades");
    }

    /**
     * 修改密码页面
     */
    @GetMapping("/change-password")
    public ModelAndView changePasswordPage(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"TEACHER".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        ModelAndView mv = new ModelAndView("teacher/change-password");
        mv.addObject("user", user);
        return mv;
    }

    /**
     * 修改密码处理
     */
    @PostMapping("/change-password")
    public ModelAndView changePassword(
            @RequestParam("oldPassword") String oldPassword,
            @RequestParam("newPassword") String newPassword,
            @RequestParam("confirmPassword") String confirmPassword,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"TEACHER".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 验证旧密码
        if (!oldPassword.equals(user.getPassword())) {
            ModelAndView mv = new ModelAndView("teacher/change-password");
            mv.addObject("user", user);
            mv.addObject("error", "原密码错误");
            return mv;
        }

        // 验证新密码确认
        if (!newPassword.equals(confirmPassword)) {
            ModelAndView mv = new ModelAndView("teacher/change-password");
            mv.addObject("user", user);
            mv.addObject("error", "新密码与确认密码不一致");
            return mv;
        }

        // 更新密码
        user.setPassword(newPassword);
        boolean success = userService.updateById(user);

        if (success) {
            // 更新session中的用户信息
            user.setPassword(newPassword);
            session.setAttribute("user", user);
            // 显示成功消息
            ModelAndView mv = new ModelAndView("teacher/change-password");
            mv.addObject("user", user);
            mv.addObject("success", "密码修改成功！");
            return mv;
        } else {
            ModelAndView mv = new ModelAndView("teacher/change-password");
            mv.addObject("user", user);
            mv.addObject("error", "密码修改失败，请重试");
            return mv;
        }
    }

    /**
     * 多条件查询成绩
     */
    @GetMapping("/search-grades")
    public ModelAndView searchGrades(
            @RequestParam(value = "studentName", required = false) String studentName,
            @RequestParam(value = "courseName", required = false) String courseName,
            @RequestParam(value = "studentNumber", required = false) String studentNumber,
            @RequestParam(value = "classId", required = false) Long classId,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"TEACHER".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 根据用户ID获取教师信息
        Teacher teacher = teacherService.getByUserId(user.getId());
        if (teacher == null) {
            return new ModelAndView("error", "message", "教师信息不存在");
        }

        List<Map<String, Object>> grades;

        if (classId != null) {
            // 按班级查询
            grades = gradeService.findGradesByClassId(classId);
        } else if ((studentName != null && !studentName.trim().isEmpty()) ||
                (courseName != null && !courseName.trim().isEmpty()) ||
                (studentNumber != null && !studentNumber.trim().isEmpty())) {
            // 按条件查询
            grades = gradeService.findGradesByConditions(
                    studentName != null ? studentName.trim() : null,
                    courseName != null ? courseName.trim() : null,
                    studentNumber != null ? studentNumber.trim() : null
            );
        } else {
            // 显示该教师授课的学生成绩（与成绩管理页面保持一致）
            List<Map<String, Object>> courses = gradeService.findCoursesByTeacherId(teacher.getId());
            if (!courses.isEmpty()) {
                // 获取第一门课程的ID（因为每个老师只教一门课）
                Long courseId = ((Number) courses.get(0).get("id")).longValue();
                grades = gradeService.findGradesByTeacherIdAndCourse(teacher.getId(), courseId);
            } else {
                grades = new ArrayList<>();
            }
        }

        // 获取所有班级用于下拉选择
        List<com.student.entity.Class> classes = classService.getAllClasses();

        ModelAndView mv = new ModelAndView("teacher/search-grades");
        mv.addObject("user", user);
        mv.addObject("grades", grades);
        mv.addObject("classes", classes);
        mv.addObject("studentName", studentName);
        mv.addObject("courseName", courseName);
        mv.addObject("studentNumber", studentNumber);
        mv.addObject("classId", classId);
        return mv;
    }

    /**
     * 编辑成绩页面
     */
    @GetMapping("/edit-grade/{gradeId}")
    public ModelAndView editGradePage(@PathVariable Long gradeId, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"TEACHER".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        Grade grade = gradeService.getById(gradeId);
        if (grade == null) {
            return new ModelAndView("error", "message", "成绩记录不存在");
        }

        ModelAndView mv = new ModelAndView("teacher/edit-grade");
        mv.addObject("user", user);
        mv.addObject("grade", grade);
        mv.addObject("courses", courseService.list());
        return mv;
    }

    /**
     * 更新成绩
     */
    @PostMapping("/update-grade")
    public ModelAndView updateGrade(
            @RequestParam Long gradeId,
            @RequestParam BigDecimal score,
            @RequestParam String academicYear,
            @RequestParam Integer semester,
            @RequestParam Long studentId,
            @RequestParam Long courseId,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"TEACHER".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        try {
            Grade grade = gradeService.getById(gradeId);

            if (grade != null) {
                // 验证成绩记录是否属于当前教师
                Teacher teacher = teacherService.getByUserId(user.getId());
                if (teacher == null) {
                    String errorMsg = URLEncoder.encode("教师信息不存在", StandardCharsets.UTF_8);
                    return new ModelAndView("redirect:/teacher/grades?error=" + errorMsg);
                }

                // 验证教师是否有权限修改这个课程的成绩
                Course course = courseService.getById(courseId);
                if (course == null || !teacher.getId().equals(course.getTeacherId())) {
                    String errorMsg = URLEncoder.encode("您没有权限修改此课程的成绩", StandardCharsets.UTF_8);
                    return new ModelAndView("redirect:/teacher/grades?error=" + errorMsg);
                }

                grade.setScore(score);
                grade.setAcademicYear(academicYear);
                grade.setSemester(semester);

                boolean success = gradeService.updateById(grade);
                if (success) {
                    return new ModelAndView("redirect:/teacher/grades");
                } else {
                    String errorMsg = URLEncoder.encode("成绩修改失败", StandardCharsets.UTF_8);
                    return new ModelAndView("redirect:/teacher/grades?error=" + errorMsg);
                }
            } else {
                // 成绩记录不存在，创建新的成绩记录
                Teacher teacher = teacherService.getByUserId(user.getId());
                if (teacher == null) {
                    String errorMsg = URLEncoder.encode("教师信息不存在", StandardCharsets.UTF_8);
                    return new ModelAndView("redirect:/teacher/grades?error=" + errorMsg);
                }

                // 验证教师是否有权限为这个课程录入成绩
                Course course = courseService.getById(courseId);
                if (course == null || !teacher.getId().equals(course.getTeacherId())) {
                    String errorMsg = URLEncoder.encode("您没有权限为此课程录入成绩", StandardCharsets.UTF_8);
                    return new ModelAndView("redirect:/teacher/grades?error=" + errorMsg);
                }

                Grade newGrade = new Grade();
                newGrade.setStudentId(studentId);
                newGrade.setCourseId(courseId);
                newGrade.setScore(score);
                newGrade.setAcademicYear(academicYear);
                newGrade.setSemester(semester);
                newGrade.setIsAbnormal(0); // 默认为正常

                boolean success = gradeService.save(newGrade);
                if (success) {
                    return new ModelAndView("redirect:/teacher/grades");
                } else {
                    String errorMsg = URLEncoder.encode("成绩录入失败", StandardCharsets.UTF_8);
                    return new ModelAndView("redirect:/teacher/grades?error=" + errorMsg);
                }
            }
        } catch (Exception e) {
            String errorMsg = URLEncoder.encode("成绩修改失败，请稍后重试", StandardCharsets.UTF_8);
            return new ModelAndView("redirect:/teacher/grades?error=" + errorMsg);
        }
    }
}