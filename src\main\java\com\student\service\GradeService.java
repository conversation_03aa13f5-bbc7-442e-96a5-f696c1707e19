package com.student.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.student.entity.Grade;
import com.student.mapper.GradeMapper;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

/**
 * 成绩服务类
 */
@Service
public class GradeService extends ServiceImpl<GradeMapper, Grade> {
    
    /**
     * 查询学生成绩（包含课程信息）
     */
    public List<Map<String, Object>> findStudentGradesWithCourse(Long studentId) {
        return baseMapper.findStudentGradesWithCourse(studentId);
    }
    
    /**
     * 查询所有成绩（包含学生和课程信息）
     */
    public List<Map<String, Object>> findAllGradesWithDetails() {
        return baseMapper.findAllGradesWithDetails();
    }

    /**
     * 按课程名查询学生成绩
     */
    public List<Map<String, Object>> findStudentGradesByCourse(Long studentId, String courseName) {
        return baseMapper.findStudentGradesByCourse(studentId, courseName);
    }

    /**
     * 按多条件查询成绩（教师用）
     */
    public List<Map<String, Object>> findGradesByConditions(String studentName, String courseName, String studentNumber) {
        return baseMapper.findGradesByConditions(studentName, courseName, studentNumber);
    }

    /**
     * 按班级号查询成绩
     */
    public List<Map<String, Object>> findGradesByClassId(Long classId) {
        return baseMapper.findGradesByClassId(classId);
    }

    /**
     * 查询学生课程表
     */
    public List<Map<String, Object>> findStudentCourseSchedule(Long studentId) {
        return baseMapper.findStudentCourseSchedule(studentId);
    }

    /**
     * 根据教师姓名查询该教师授课的课程列表
     */
    public List<Map<String, Object>> findCoursesByTeacherName(String teacherName) {
        return baseMapper.findCoursesByTeacherName(teacherName);
    }

    /**
     * 根据教师姓名和课程ID查询该课程的学生成绩
     */
    public List<Map<String, Object>> findGradesByTeacherAndCourse(String teacherName, Long courseId) {
        return baseMapper.findGradesByTeacherAndCourse(teacherName, courseId);
    }

    /**
     * 根据教师ID查询该教师授课的课程列表
     */
    public List<Map<String, Object>> findCoursesByTeacherId(Long teacherId) {
        return baseMapper.findCoursesByTeacherId(teacherId);
    }

    /**
     * 根据教师ID和课程ID查询该课程的学生成绩
     */
    public List<Map<String, Object>> findGradesByTeacherIdAndCourse(Long teacherId, Long courseId) {
        return baseMapper.findGradesByTeacherIdAndCourse(teacherId, courseId);
    }

    /**
     * 查询学生的课程信息（包含教师信息）
     */
    public List<Map<String, Object>> findStudentCoursesWithTeacher(Long studentId) {
        return baseMapper.findStudentCoursesWithTeacher(studentId);
    }

    /**
     * 删除学生的所有成绩记录
     */
    public void deleteGradesByStudentId(Long studentId) {
        baseMapper.deleteGradesByStudentId(studentId);
    }
}
