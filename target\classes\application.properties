# 数据库配置 - 确保DataSource正确配置
spring.datasource.url=***************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Tjygk1156
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 数据库连接池配置
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000

# 应用名称
spring.application.name=grade-system

# 服务器端口
server.port=8080

# Thymeleaf配置
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.cache=false

# MyBatis Plus配置
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.id-type=AUTO

# 日志配置
logging.level.com.student=DEBUG
logging.level.root=INFO
logging.level.org.springframework.boot.autoconfigure=DEBUG
