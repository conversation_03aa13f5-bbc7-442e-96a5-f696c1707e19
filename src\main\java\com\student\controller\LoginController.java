package com.student.controller;

import com.student.entity.User;
import com.student.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpSession;

/**
 * 登录控制器
 */
@Controller
public class LoginController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 显示登录页面
     */
    @GetMapping({"/", "/login"})
    public String loginPage() {
        return "login";
    }
    
    /**
     * 处理登录请求
     */
    @PostMapping("/login")
    public ModelAndView login(@RequestParam String username,
                             @RequestParam String password,
                             HttpSession session) {

        System.out.println("=== 登录调试信息 ===");
        try {
            User user = userService.login(username, password);

            if (user != null) {
                // 登录成功，保存用户信息到session
                session.setAttribute("user", user);

                // 根据身份跳转到不同页面
                switch (user.getRole()) {
                    case "STUDENT":
                        return new ModelAndView("redirect:/student/dashboard");
                    case "TEACHER":
                        return new ModelAndView("redirect:/teacher/dashboard");
                    case "ADMIN":
                        return new ModelAndView("redirect:/admin/dashboard");
                    default:
                        return new ModelAndView("login", "error", "未知身份");
                }
            } else {
                // 登录失败
                return new ModelAndView("login", "error", "用户名或密码错误");
            }
        } catch (Exception e) {
            return new ModelAndView("login", "error", "系统异常，请稍后重试");
        }
    }
    
    /**
     * 退出登录
     */
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate();
        return "redirect:/login";
    }
}
