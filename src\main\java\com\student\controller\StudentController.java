package com.student.controller;

import com.student.entity.Student;
import com.student.entity.User;
import com.student.service.GradeService;
import com.student.service.StudentService;
import com.student.service.UserService;
import com.student.service.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 学生控制器
 */
@Controller
@RequestMapping("/student")
public class StudentController {
    
    @Autowired
    private StudentService studentService;

    @Autowired
    private GradeService gradeService;

    @Autowired
    private UserService userService;

    @Autowired
    private CourseService courseService;
    
    /**
     * 学生主页
     */
    @GetMapping("/dashboard")
    public ModelAndView dashboard(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"STUDENT".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }
        
        ModelAndView mv = new ModelAndView("student/dashboard");
        mv.addObject("user", user);
        return mv;
    }
    
    /**
     * 查看我的成绩
     */
    @GetMapping("/grades")
    public ModelAndView viewGrades(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"STUDENT".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 查找学生信息
        Student student = studentService.getByUserId(user.getId());
        if (student == null) {
            return new ModelAndView("error", "message", "学生信息不存在");
        }

        // 查询成绩
        List<Map<String, Object>> grades = gradeService.findStudentGradesWithCourse(student.getId());

        ModelAndView mv = new ModelAndView("student/grades");
        mv.addObject("user", user);
        mv.addObject("student", student);
        mv.addObject("grades", grades);
        return mv;
    }

    /**
     * 学生课程信息
     */
    @GetMapping("/courses")
    public ModelAndView viewCourses(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"STUDENT".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 查找学生信息
        Student student = studentService.getByUserId(user.getId());
        if (student == null) {
            return new ModelAndView("error", "message", "学生信息不存在");
        }

        // 查询学生的课程信息（通过成绩表关联获取课程和教师信息）
        List<Map<String, Object>> courses = gradeService.findStudentCoursesWithTeacher(student.getId());

        ModelAndView mv = new ModelAndView("student/courses");
        mv.addObject("user", user);
        mv.addObject("student", student);
        mv.addObject("courses", courses);
        return mv;
    }

    /**
     * 修改密码页面
     */
    @GetMapping("/change-password")
    public ModelAndView changePasswordPage(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null || !"STUDENT".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        ModelAndView mv = new ModelAndView("student/change-password");
        mv.addObject("user", user);
        return mv;
    }

    /**
     * 修改密码处理
     */
    @PostMapping("/change-password")
    public ModelAndView changePassword(
            @RequestParam("oldPassword") String oldPassword,
            @RequestParam("newPassword") String newPassword,
            @RequestParam("confirmPassword") String confirmPassword,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"STUDENT".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 验证旧密码
        if (!oldPassword.equals(user.getPassword())) {
            ModelAndView mv = new ModelAndView("student/change-password");
            mv.addObject("user", user);
            mv.addObject("error", "原密码错误");
            return mv;
        }

        // 验证新密码确认
        if (!newPassword.equals(confirmPassword)) {
            ModelAndView mv = new ModelAndView("student/change-password");
            mv.addObject("user", user);
            mv.addObject("error", "新密码与确认密码不一致");
            return mv;
        }

        // 更新密码
        user.setPassword(newPassword);
        boolean success = userService.updateById(user);

        if (success) {
            // 更新session中的用户信息
            user.setPassword(newPassword);
            session.setAttribute("user", user);
            // 显示成功消息
            ModelAndView mv = new ModelAndView("student/change-password");
            mv.addObject("user", user);
            mv.addObject("success", "密码修改成功！");
            return mv;
        } else {
            ModelAndView mv = new ModelAndView("student/change-password");
            mv.addObject("user", user);
            mv.addObject("error", "密码修改失败，请重试");
            return mv;
        }
    }

    /**
     * 按课程名查询成绩
     */
    @GetMapping("/search-grades")
    public ModelAndView searchGrades(
            @RequestParam(value = "courseName", required = false) String courseName,
            HttpSession session) {

        User user = (User) session.getAttribute("user");
        if (user == null || !"STUDENT".equals(user.getRole())) {
            return new ModelAndView("redirect:/login");
        }

        // 查找学生信息
        Student student = studentService.getByUserId(user.getId());
        if (student == null) {
            return new ModelAndView("error", "message", "学生信息不存在");
        }

        List<Map<String, Object>> grades;
        if (courseName != null && !courseName.trim().isEmpty()) {
            // 按课程名搜索成绩
            grades = gradeService.findStudentGradesByCourse(student.getId(), courseName.trim());
        } else {
            // 显示所有成绩
            grades = gradeService.findStudentGradesWithCourse(student.getId());
        }

        ModelAndView mv = new ModelAndView("student/search-grades");
        mv.addObject("user", user);
        mv.addObject("student", student);
        mv.addObject("grades", grades);
        mv.addObject("courseName", courseName);
        return mv;
    }
}
