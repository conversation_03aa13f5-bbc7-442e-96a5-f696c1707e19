<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的成绩</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .header {
            background-color: #007bff;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .search-box {
            margin-bottom: 20px;
        }

        .search-box input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/student/dashboard" class="back-btn">← 返回主页</a>

            <h2>我的成绩</h2>

            <div class="search-box">
                <input type="text" id="searchCourse" placeholder="按课程名搜索..." onkeyup="searchTable()">
            </div>

            <table id="gradesTable">
                <thead>
                    <tr>
                        <th>课程名称</th>
                        <th>成绩</th>
                        <th>学分</th>
                        <th>学期</th>
                        <th>学年</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="grade : ${grades}">
                        <td th:text="${grade.course_name}"></td>
                        <td th:text="${grade.score}"></td>
                        <td th:text="${grade.credits}"></td>
                        <td th:text="${grade.semester}"></td>
                        <td th:text="${grade.academic_year}"></td>
                    </tr>
                </tbody>
            </table>

            <div th:if="${#lists.isEmpty(grades)}" style="text-align: center; padding: 50px; color: #666;">
                暂无成绩记录
            </div>
        </div>
    </div>

    <script>
        function searchTable() {
            var courseFilter = document.getElementById("searchCourse").value.toUpperCase();
            var table = document.getElementById("gradesTable");
            var tr = table.getElementsByTagName("tr");

            for (var i = 1; i < tr.length; i++) {
                var courseName = tr[i].getElementsByTagName("td")[0]; // 课程名称

                if (courseName) {
                    var courseText = courseName.textContent || courseName.innerText;

                    if (courseText.toUpperCase().indexOf(courseFilter) > -1) {
                        tr[i].style.display = "";
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            }
        }
    </script>
</body>

</html>