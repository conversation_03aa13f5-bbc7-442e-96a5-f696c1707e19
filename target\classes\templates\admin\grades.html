<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成绩审核</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .header {
            background-color: #dc3545;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .search-box {
            margin-bottom: 20px;
        }

        .search-box input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            width: 200px;
        }

        .btn {
            padding: 5px 10px;
            margin-right: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            color: white;
        }

        .btn-info {
            background-color: #17a2b8;
        }

        .btn-success {
            background-color: #28a745;
        }

        .btn-warning {
            background-color: #ffc107;
        }

        .status-normal {
            color: #28a745;
            font-weight: bold;
        }

        .status-abnormal {
            color: #dc3545;
            font-weight: bold;
        }

        .row-abnormal {
            background-color: #fff5f5;
            border-left: 4px solid #dc3545;
        }

        .row-normal {
            background-color: #f8fff8;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/admin/dashboard" class="back-btn">← 返回主页</a>

            <h2>成绩审核</h2>

            <!-- 错误信息显示 -->
            <div th:if="${error}"
                style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; border: 1px solid #f5c6cb;">
                <strong>错误：</strong> <span th:text="${error}"></span>
            </div>

            <div class="search-box">
                <input type="text" id="searchStudent" placeholder="按学生姓名搜索..." onkeyup="searchTable()">
                <input type="text" id="searchCourse" placeholder="按课程名搜索..." onkeyup="searchTable()">
                <input type="text" id="searchNumber" placeholder="按学号搜索..." onkeyup="searchTable()">
                <select id="filterClass" onchange="searchTable()"
                    style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;">
                    <option value="">所有班级</option>
                    <option th:each="class : ${classes}" th:value="${class.className}" th:text="${class.className}">班级名称
                    </option>
                </select>
                <select id="filterStatus" onchange="searchTable()"
                    style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="">所有状态</option>
                    <option value="normal">正常成绩</option>
                    <option value="abnormal">异常成绩</option>
                </select>
            </div>

            <table id="gradesTable">
                <thead>
                    <tr>
                        <th>学号</th>
                        <th>学生姓名</th>
                        <th>班级</th>
                        <th>课程名称</th>
                        <th>成绩</th>
                        <th>学期</th>
                        <th>学年</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="grade : ${grades}"
                        th:class="${grade.is_abnormal == 1 ? 'row-abnormal' : 'row-normal'}">
                        <td th:text="${grade.student_number}"></td>
                        <td th:text="${grade.student_name}"></td>
                        <td th:text="${grade.class_name}"></td>
                        <td th:text="${grade.course_name}"></td>
                        <td th:text="${grade.score}"></td>
                        <td th:text="${grade.semester}"></td>
                        <td th:text="${grade.year}"></td>
                        <td>
                            <span th:if="${grade.is_abnormal == 1}" class="status-abnormal">异常</span>
                            <span th:unless="${grade.is_abnormal == 1}" class="status-normal">正常</span>
                        </td>
                        <td>
                            <!-- 正常成绩显示标记按钮 -->
                            <button th:if="${grade.is_abnormal == 0}" class="btn btn-warning"
                                th:onclick="'markAsAbnormal(' + ${grade.id} + ')'" title="将正常成绩标记为异常">
                                标记
                            </button>
                            <!-- 异常成绩显示复核按钮 -->
                            <button th:if="${grade.is_abnormal == 1}" class="btn btn-info"
                                th:onclick="'reviewAbnormal(' + ${grade.id} + ')'" title="对异常成绩进行复核">
                                复核
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div th:if="${#lists.isEmpty(grades)}" style="text-align: center; padding: 50px; color: #666;">
                暂无成绩记录
            </div>
        </div>
    </div>



    <script>
        function searchTable() {
            var studentFilter = document.getElementById("searchStudent").value.toUpperCase();
            var courseFilter = document.getElementById("searchCourse").value.toUpperCase();
            var numberFilter = document.getElementById("searchNumber").value.toUpperCase();
            var classFilter = document.getElementById("filterClass").value.toUpperCase();
            var statusFilter = document.getElementById("filterStatus").value;
            var table = document.getElementById("gradesTable");
            var tr = table.getElementsByTagName("tr");

            for (var i = 1; i < tr.length; i++) {
                var studentNumber = tr[i].getElementsByTagName("td")[0];
                var studentName = tr[i].getElementsByTagName("td")[1];
                var className = tr[i].getElementsByTagName("td")[2];
                var courseName = tr[i].getElementsByTagName("td")[3];
                var statusElement = tr[i].getElementsByTagName("td")[7];

                if (studentName && courseName && studentNumber && className && statusElement) {
                    var studentText = studentName.textContent || studentName.innerText;
                    var courseText = courseName.textContent || courseName.innerText;
                    var numberText = studentNumber.textContent || studentNumber.innerText;
                    var classText = className.textContent || className.innerText;
                    var statusText = statusElement.textContent || statusElement.innerText;

                    var statusMatch = true;
                    if (statusFilter === "normal") {
                        statusMatch = statusText.indexOf("正常") > -1;
                    } else if (statusFilter === "abnormal") {
                        statusMatch = statusText.indexOf("异常") > -1;
                    }

                    if (studentText.toUpperCase().indexOf(studentFilter) > -1 &&
                        courseText.toUpperCase().indexOf(courseFilter) > -1 &&
                        numberText.toUpperCase().indexOf(numberFilter) > -1 &&
                        classText.toUpperCase().indexOf(classFilter) > -1 &&
                        statusMatch) {
                        tr[i].style.display = "";
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            }
        }

        // 标记正常成绩为异常
        function markAsAbnormal(gradeId) {
            console.log("标记成绩为异常 - ID:", gradeId);

            if (confirm("是否确认标记为异常？")) {
                console.log("执行标记操作");
                markGradeAjax(gradeId, "标记为异常");
            } else {
                console.log("用户取消了标记操作");
            }
        }

        // 复核异常成绩
        function reviewAbnormal(gradeId) {
            console.log("复核异常成绩 - ID:", gradeId);

            // 弹出选择框让管理员选择
            var choice = confirm("是否确认改为正常？");

            if (choice) {
                // 用户选择改为正常
                console.log("复核结果：改为正常");
                markGradeAjax(gradeId, "审核为正常");
            } else {
                // 用户选择保持异常
                console.log("复核结果：保持异常");
            }
        }

        // AJAX函数：成绩审核
        function markGradeAjax(gradeId, action) {
            console.log("发送成绩审核请求 - ID:", gradeId, "操作:", action);

            fetch('/admin/grades/mark/' + gradeId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: action
                })
            })
                .then(response => {
                    console.log("响应状态:", response.status);
                    if (!response.ok) {
                        throw new Error('HTTP ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("服务器响应:", data);
                    if (data.success) {
                        // 操作成功，直接刷新页面显示最新状态
                        location.reload();
                    } else {
                        alert('操作失败：' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error("审核操作错误:", error);
                    alert('网络错误：' + error.message);
                });
        }


    </script>
</body>

</html>