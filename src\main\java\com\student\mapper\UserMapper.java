package com.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.student.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据用户名查找用户
     */
    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(String username);
    
    /**
     * 根据用户名和密码查找用户
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND password = #{password}")
    User findByUsernameAndPassword(String username, String password);

    /**
     * 根据角色查询用户
     */
    @Select("SELECT * FROM users WHERE role = #{role} ORDER BY real_name")
    List<User> findByRole(String role);

    /**
     * 搜索用户（支持姓名、用户名、角色筛选）
     */
    @Select("<script>" +
            "SELECT * FROM users WHERE 1=1 " +
            "<if test='realName != null and realName != \"\"'>" +
            "AND real_name LIKE CONCAT('%', #{realName}, '%') " +
            "</if>" +
            "<if test='username != null and username != \"\"'>" +
            "AND username LIKE CONCAT('%', #{username}, '%') " +
            "</if>" +
            "<if test='role != null and role != \"\"'>" +
            "AND role = #{role} " +
            "</if>" +
            "ORDER BY real_name" +
            "</script>")
    List<User> searchUsers(@Param("realName") String realName,
                          @Param("username") String username,
                          @Param("role") String role);
}
