<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程信息</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .header {
            background-color: #007bff;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            font-weight: normal;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #0056b3;
            text-decoration: none;
            color: white;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .table-container {
            overflow-x: auto;
        }

        .course-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .course-table th,
        .course-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .course-table th {
            font-weight: normal;
        }

        .course-table tr:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/student/dashboard" class="back-btn">← 返回主页</a>

            <h2>课程信息</h2>

            <div class="table-container">
                <table class="course-table" th:if="${!#lists.isEmpty(courses)}">
                    <thead>
                        <tr>
                            <th>课程名称</th>
                            <th>授课老师</th>
                            <th>教学地点</th>
                            <th>学分</th>
                            <th>学时</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="course : ${courses}">
                            <td th:text="${course.courseName ?: '未设置'}"></td>
                            <td th:text="${course.teacherName ?: '未分配'}"></td>
                            <td th:text="${course.classroom ?: '待安排'}"></td>
                            <td th:text="${course.credits ?: '0'}"></td>
                            <td th:text="${course.teachingHours ?: '0'}"></td>
                        </tr>
                    </tbody>
                </table>

                <div th:if="${#lists.isEmpty(courses)}" style="text-align: center; padding: 50px; color: #666;">
                    暂无课程记录
                </div>
            </div>
        </div>
    </div>
</body>

</html>