package com.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.student.entity.Class;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 班级数据访问层
 */
@Mapper
public interface ClassMapper extends BaseMapper<Class> {
    
    /**
     * 查询所有班级信息
     */
    @Select("SELECT * FROM classes ORDER BY grade_year DESC, class_name")
    List<Class> selectAllClasses();
    
    /**
     * 根据年级查询班级
     */
    @Select("SELECT * FROM classes WHERE grade_year = #{gradeYear} ORDER BY class_name")
    List<Class> selectByGradeYear(Integer gradeYear);
    
    /**
     * 根据专业查询班级
     */
    @Select("SELECT * FROM classes WHERE major = #{major} ORDER BY grade_year DESC, class_name")
    List<Class> selectByMajor(String major);
    
    /**
     * 根据班主任ID查询班级
     */
    @Select("SELECT * FROM classes WHERE teacher_id = #{teacherId}")
    List<Class> selectByTeacherId(Long teacherId);

    /**
     * 搜索班级
     */
    @Select("<script>" +
            "SELECT * FROM classes WHERE 1=1 " +
            "<if test='className != null and className != \"\"'>" +
            "AND class_name LIKE CONCAT('%', #{className}, '%') " +
            "</if>" +
            "<if test='major != null and major != \"\"'>" +
            "AND major LIKE CONCAT('%', #{major}, '%') " +
            "</if>" +
            "<if test='gradeYear != null and gradeYear != \"\"'>" +
            "AND grade_year = #{gradeYear} " +
            "</if>" +
            "ORDER BY grade_year DESC, class_name" +
            "</script>")
    List<Class> searchClasses(@Param("className") String className,
                             @Param("major") String major,
                             @Param("gradeYear") String gradeYear);

    /**
     * 清空教师的班主任职务
     */
    @Update("UPDATE classes SET teacher_id = NULL WHERE teacher_id = #{teacherId}")
    void clearTeacherFromClasses(Long teacherId);
}
