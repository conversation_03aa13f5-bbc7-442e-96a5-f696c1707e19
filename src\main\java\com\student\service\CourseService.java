package com.student.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.student.entity.Course;
import com.student.entity.Grade;
import com.student.mapper.CourseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 课程服务类
 */
@Service
public class CourseService extends ServiceImpl<CourseMapper, Course> {

    @Autowired
    private GradeService gradeService;

    /**
     * 清空教师的课程
     */
    public void clearTeacherFromCourses(Long teacherId) {
        baseMapper.clearTeacherFromCourses(teacherId);
    }

    /**
     * 安全删除课程
     * 检查课程是否有关联的成绩记录，如果有则不允许删除
     *
     * @param courseId 课程ID
     * @return 删除结果信息
     */
    public CourseDeleteResult safeDeleteCourse(Long courseId) {
        // 1. 检查课程是否存在
        Course course = getById(courseId);
        if (course == null) {
            return new CourseDeleteResult(false, "课程不存在");
        }

        // 2. 检查是否有关联的成绩记录
        LambdaQueryWrapper<Grade> gradeQuery = new LambdaQueryWrapper<>();
        gradeQuery.eq(Grade::getCourseId, courseId);
        long gradeCount = gradeService.count(gradeQuery);

        if (gradeCount > 0) {
            return new CourseDeleteResult(false,
                String.format("该课程已有 %d 条成绩记录，无法删除。请先删除相关成绩记录。", gradeCount));
        }

        // 3. 执行删除操作
        boolean success = removeById(courseId);
        if (success) {
            return new CourseDeleteResult(true, "课程删除成功");
        } else {
            return new CourseDeleteResult(false, "删除操作失败，请重试");
        }
    }

    /**
     * 课程删除结果类
     */
    public static class CourseDeleteResult {
        private final boolean success;
        private final String message;

        public CourseDeleteResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }
    }
}
