<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成绩审核测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>成绩审核测试页面</h1>
        
        <p><a href="/admin/dashboard">← 返回管理员主页</a></p>

        <!-- 错误信息显示 -->
        <div th:if="${error}" class="error">
            <strong>错误：</strong> <span th:text="${error}"></span>
        </div>

        <!-- 成功信息显示 -->
        <div th:if="${!error and grades != null}" class="success">
            <strong>成功：</strong> 成绩数据加载成功，共 <span th:text="${#lists.size(grades)}"></span> 条记录
        </div>

        <!-- 数据统计 -->
        <div style="margin-bottom: 20px;">
            <h3>数据统计</h3>
            <p>成绩记录总数：<span th:text="${grades != null ? #lists.size(grades) : 0}"></span></p>
            <p>班级总数：<span th:text="${classes != null ? #lists.size(classes) : 0}"></span></p>
            <p>课程总数：<span th:text="${courses != null ? #lists.size(courses) : 0}"></span></p>
        </div>

        <!-- 成绩列表 -->
        <h3>成绩列表</h3>
        
        <div th:if="${grades != null and !#lists.isEmpty(grades)}">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>学号</th>
                        <th>学生姓名</th>
                        <th>班级</th>
                        <th>课程名称</th>
                        <th>成绩</th>
                        <th>学期</th>
                        <th>学年</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="grade : ${grades}">
                        <td th:text="${grade.id}"></td>
                        <td th:text="${grade.student_number ?: '未知'}"></td>
                        <td th:text="${grade.student_name ?: '未知'}"></td>
                        <td th:text="${grade.class_name ?: '未分配'}"></td>
                        <td th:text="${grade.course_name ?: '未知'}"></td>
                        <td th:text="${grade.score ?: '未录入'}"></td>
                        <td th:text="${grade.semester ?: '未知'}"></td>
                        <td th:text="${grade.year ?: '未知'}"></td>
                        <td>
                            <span th:if="${grade.is_abnormal != null and grade.is_abnormal == 1}" style="color: #dc3545;">异常</span>
                            <span th:if="${grade.is_abnormal == null or grade.is_abnormal == 0}" style="color: #28a745;">正常</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div th:if="${grades == null or #lists.isEmpty(grades)}" style="text-align: center; padding: 50px; color: #666;">
            暂无成绩记录
        </div>

        <!-- 调试信息 -->
        <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
            <h4>调试信息</h4>
            <p>用户角色：<span th:text="${user != null ? user.role : '未知'}"></span></p>
            <p>用户姓名：<span th:text="${user != null ? user.realName : '未知'}"></span></p>
            <p>页面加载时间：<span th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd HH:mm:ss')}"></span></p>
        </div>
    </div>
</body>

</html>
