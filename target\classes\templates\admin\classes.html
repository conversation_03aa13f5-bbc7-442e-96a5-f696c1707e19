<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班级管理 - 管理员</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .header {
            background-color: #dc3545;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .add-btn {
            padding: 10px 20px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }

        .add-btn:hover {
            background-color: #c82333;
            text-decoration: none;
            color: white;
        }

        /* 搜索表单样式 */
        .search-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .search-form .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .search-form .form-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }

        .form-actions-row {
            margin-top: 15px;
            text-align: center;
        }

        .search-form label {
            margin-bottom: 5px;
            font-weight: bold;
        }

        .search-form input,
        .search-form select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-btn,
        .reset-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            height: fit-content;
        }

        .search-btn {
            background-color: #007bff;
            color: white;
        }

        .search-btn:hover {
            background-color: #0056b3;
        }

        .reset-btn {
            background-color: #6c757d;
            color: white;
            margin-left: 10px;
        }

        .reset-btn:hover {
            background-color: #545b62;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        tr:hover {
            background-color: #f5f5f5;
        }

        .action-btn {
            padding: 5px 10px;
            margin-right: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 12px;
        }

        .btn-edit {
            background-color: #007bff;
            color: white;
        }

        .btn-delete {
            background-color: #dc3545;
            color: white;
        }

        .btn-edit:hover {
            background-color: #0056b3;
        }

        .btn-delete:hover {
            background-color: #c82333;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .logout:hover {
            background-color: rgba(255, 255, 255, 0.3);
            text-decoration: none;
            color: white;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .form-actions {
            text-align: right;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/admin/dashboard" class="back-btn">← 返回主页</a>

            <h2>班级管理</h2>

            <!-- 搜索框 -->
            <div class="search-box" style="margin: 20px 0;">
                <input type="text" id="searchClassName" placeholder="按班级名称搜索..." onkeyup="searchTable()"
                    style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px; width: 200px;">
                <input type="text" id="searchMajor" placeholder="按专业搜索..." onkeyup="searchTable()"
                    style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px;">
            </div>

            <button class="add-btn" onclick="showAddClassModal()">+ 添加班级</button>
            <button class="add-btn" onclick="showStudentAssignmentModal()"
                style="background-color: #28a745; margin-left: 10px;">分配学生</button>

            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>班级名称</th>
                        <th>班级代码</th>
                        <th>年级</th>
                        <th>专业</th>
                        <th>班主任</th>
                        <th>学生人数</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="class : ${classes}">
                        <td th:text="${class.id}"></td>
                        <td th:text="${class.className}"></td>
                        <td th:text="${class.classCode}"></td>
                        <td th:text="${class.gradeYear}"></td>
                        <td th:text="${class.major}"></td>
                        <td th:text="${class.teacherName}"></td>
                        <td th:text="${class.studentCount}"></td>
                        <td>
                            <button onclick="editClass(this)" th:data-class-id="${class.id}"
                                th:data-class-name="${class.className}" th:data-class-code="${class.classCode}"
                                th:data-grade-year="${class.gradeYear}" th:data-major="${class.major}"
                                th:data-teacher-id="${class.teacherId}" class="action-btn btn-edit">编辑</button>
                            <button onclick="deleteClass(this)" th:data-class-id="${class.id}"
                                th:data-class-name="${class.className}" class="action-btn btn-delete">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div th:if="${#lists.isEmpty(classes)}" style="text-align: center; padding: 50px; color: #666;">
                暂无班级记录
            </div>
        </div>
    </div>

    <!-- 添加班级模态框 -->
    <div id="addClassModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加班级</h3>
                <span class="close" onclick="closeModal('addClassModal')">&times;</span>
            </div>
            <form action="/admin/classes/add" method="post">
                <div class="form-group">
                    <label for="addClassName">班级名称 *</label>
                    <input type="text" id="addClassName" name="className" required>
                </div>
                <div class="form-group">
                    <label for="addClassCode">班级代码 *</label>
                    <input type="text" id="addClassCode" name="classCode" required>
                </div>
                <div class="form-group">
                    <label for="addGradeYear">年级 *</label>
                    <select id="addGradeYear" name="gradeYear" required>
                        <option value="">请选择年级</option>
                        <option value="2024">2024级</option>
                        <option value="2023">2023级</option>
                        <option value="2022">2022级</option>
                        <option value="2021">2021级</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="addMajor">专业 *</label>
                    <input type="text" id="addMajor" name="major" required>
                </div>
                <div class="form-group">
                    <label for="addTeacherId">班主任</label>
                    <select id="addTeacherId" name="teacherId">
                        <option value="">请选择班主任</option>
                        <option th:each="teacher : ${teachers}" th:value="${teacher.id}"
                            th:text="${teacher.teacherName}">
                        </option>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal('addClassModal')">取消</button>
                    <button type="submit" class="btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>



    <!-- 编辑班级模态框 -->
    <div id="editClassModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑班级</h3>
                <span class="close" onclick="closeModal('editClassModal')">&times;</span>
            </div>
            <form id="editClassForm" action="/admin/classes/update" method="post">
                <input type="hidden" id="editClassId" name="classId">

                <div class="form-group">
                    <label for="editClassName">班级名称 *</label>
                    <input type="text" id="editClassName" name="className" required>
                </div>
                <div class="form-group">
                    <label for="editClassCode">班级代码 *</label>
                    <input type="text" id="editClassCode" name="classCode" required>
                </div>
                <div class="form-group">
                    <label for="editGradeYear">年级 *</label>
                    <select id="editGradeYear" name="gradeYear" required>
                        <option value="">请选择年级</option>
                        <option value="2024">2024级</option>
                        <option value="2023">2023级</option>
                        <option value="2022">2022级</option>
                        <option value="2021">2021级</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editMajor">专业 *</label>
                    <input type="text" id="editMajor" name="major" required>
                </div>
                <div class="form-group">
                    <label for="editTeacherId">班主任</label>
                    <select id="editTeacherId" name="teacherId">
                        <option value="">请选择班主任</option>
                        <option th:each="teacher : ${teachers}" th:value="${teacher.id}"
                            th:text="${teacher.teacherName}">
                        </option>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal('editClassModal')">取消</button>
                    <button type="submit" class="btn-primary">保存修改</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 学生分配模态框 -->
    <div id="studentAssignmentModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3>学生分配管理</h3>
                <span class="close" onclick="closeModal('studentAssignmentModal')">&times;</span>
            </div>
            <div>
                <!-- 搜索框 -->
                <div style="margin-bottom: 20px;">
                    <input type="text" id="studentSearchInput" placeholder="搜索学生姓名..." onkeyup="filterStudents()"
                        style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">
                </div>

                <!-- 学生列表 -->
                <div style="border: 1px solid #ddd; border-radius: 4px; max-height: 400px; overflow-y: auto;">
                    <table style="width: 100%; margin: 0;">
                        <thead>
                            <tr style="background-color: #f8f9fa;">
                                <th style="padding: 10px; width: 60px;">序号</th>
                                <th style="padding: 10px;">姓名</th>
                                <th style="padding: 10px;">学号</th>
                                <th style="padding: 10px;">当前班级</th>
                                <th style="padding: 10px; width: 100px;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="studentAssignmentList">
                            <!-- 学生列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-secondary" onclick="closeModal('studentAssignmentModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 编辑学生班级模态框 -->
    <div id="editStudentClassModal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3>修改学生班级</h3>
                <span class="close" onclick="closeModal('editStudentClassModal')">&times;</span>
            </div>
            <div>
                <div class="form-group">
                    <label>学生姓名：</label>
                    <span id="editStudentName" style="font-weight: bold;"></span>
                </div>
                <div class="form-group">
                    <label>学号：</label>
                    <span id="editStudentNumber" style="font-weight: bold;"></span>
                </div>
                <div class="form-group">
                    <label for="editStudentClassSelect">选择班级：</label>
                    <select id="editStudentClassSelect"
                        style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="">未分配班级</option>
                        <!-- 班级选项将通过JavaScript动态加载 -->
                    </select>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-secondary" onclick="closeModal('editStudentClassModal')">取消</button>
                <button type="button" class="btn-primary" onclick="saveStudentClassAssignment()">保存</button>
            </div>
        </div>
    </div>

    <script>
        function searchTable() {
            var classNameFilter = document.getElementById("searchClassName").value.toUpperCase();
            var majorFilter = document.getElementById("searchMajor").value.toUpperCase();
            var table = document.querySelector("table");
            var tr = table.getElementsByTagName("tr");

            for (var i = 1; i < tr.length; i++) {
                var className = tr[i].getElementsByTagName("td")[1];
                var major = tr[i].getElementsByTagName("td")[4];

                if (className && major) {
                    var classNameText = className.textContent || className.innerText;
                    var majorText = major.textContent || major.innerText;

                    if (classNameText.toUpperCase().indexOf(classNameFilter) > -1 &&
                        majorText.toUpperCase().indexOf(majorFilter) > -1) {
                        tr[i].style.display = "";
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            }
        }

        function showAddClassModal() {
            document.getElementById('addClassModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }



        function editClass(btn) {
            var classId = btn.getAttribute('data-class-id');
            var className = btn.getAttribute('data-class-name');
            var classCode = btn.getAttribute('data-class-code');
            var gradeYear = btn.getAttribute('data-grade-year');
            var major = btn.getAttribute('data-major');
            var teacherId = btn.getAttribute('data-teacher-id');

            // 填充编辑表单
            document.getElementById('editClassId').value = classId;
            document.getElementById('editClassName').value = className;
            document.getElementById('editClassCode').value = classCode;
            document.getElementById('editGradeYear').value = gradeYear;
            document.getElementById('editMajor').value = major;
            document.getElementById('editTeacherId').value = teacherId || '';

            document.getElementById('editClassModal').style.display = 'block';
        }

        function deleteClass(btn) {
            var classId = btn.getAttribute('data-class-id');

            if (confirm('是否确认删除？')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/classes/delete/' + classId;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 全局变量
        var allStudents = [];
        var allClasses = [];
        var currentEditingStudentId = null;

        function showStudentAssignmentModal() {
            loadAllStudentsAndClasses();
            document.getElementById('studentAssignmentModal').style.display = 'block';
        }

        function loadAllStudentsAndClasses() {
            // 加载所有学生数据
            fetch('/admin/students/all')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        allStudents = data.students;
                        displayStudentAssignmentList();
                    } else {
                        alert('获取学生数据失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取学生数据失败');
                });

            // 加载所有班级数据
            fetch('/admin/classes/all')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        allClasses = data.classes;
                        populateClassSelect();
                    } else {
                        alert('获取班级数据失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取班级数据失败');
                });
        }

        function displayStudentAssignmentList() {
            var container = document.getElementById('studentAssignmentList');
            var html = '';

            if (allStudents && allStudents.length > 0) {
                allStudents.forEach(function (student, index) {
                    var className = student.className || '未分配';
                    html += '<tr>';
                    html += '<td style="padding: 10px;">' + (index + 1) + '</td>';
                    html += '<td style="padding: 10px;">' + student.studentName + '</td>';
                    html += '<td style="padding: 10px;">' + student.studentNumber + '</td>';
                    html += '<td style="padding: 10px;">' + className + '</td>';
                    html += '<td style="padding: 10px;">';
                    html += '<button onclick="editStudentClass(' + student.id + ', \'' + student.studentName + '\', \'' + student.studentNumber + '\', ' + (student.classId || 'null') + ')" ';
                    html += 'style="padding: 5px 10px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">编辑</button>';
                    html += '</td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="5" style="text-align: center; padding: 20px; color: #666;">暂无学生记录</td></tr>';
            }

            container.innerHTML = html;
        }

        function filterStudents() {
            var searchValue = document.getElementById('studentSearchInput').value.toUpperCase();
            var rows = document.querySelectorAll('#studentAssignmentList tr');

            rows.forEach(function (row) {
                var studentName = row.cells[1].textContent || row.cells[1].innerText;
                if (studentName.toUpperCase().indexOf(searchValue) > -1) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function populateClassSelect() {
            var select = document.getElementById('editStudentClassSelect');
            var html = '<option value="">未分配班级</option>';

            if (allClasses && allClasses.length > 0) {
                allClasses.forEach(function (clazz) {
                    html += '<option value="' + clazz.id + '">' + clazz.className + '</option>';
                });
            }

            select.innerHTML = html;
        }

        function editStudentClass(studentId, studentName, studentNumber, currentClassId) {
            currentEditingStudentId = studentId;
            document.getElementById('editStudentName').textContent = studentName;
            document.getElementById('editStudentNumber').textContent = studentNumber;
            document.getElementById('editStudentClassSelect').value = currentClassId || '';
            document.getElementById('editStudentClassModal').style.display = 'block';
        }

        function saveStudentClassAssignment() {
            if (!currentEditingStudentId) return;

            var newClassId = document.getElementById('editStudentClassSelect').value || null;

            fetch('/admin/students/' + currentEditingStudentId + '/assign-class', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    classId: newClassId
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        closeModal('editStudentClassModal');
                        loadAllStudentsAndClasses(); // 重新加载数据
                    } else {
                        alert('分配失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('分配失败');
                });
        }

        function loadStudentsData() {
            if (!currentClassId) return;

            fetch('/admin/classes/' + currentClassId + '/students/data')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayClassStudents(data.classStudents);
                        displayUnassignedStudents(data.unassignedStudents);
                    } else {
                        alert('获取学生数据失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取学生数据失败');
                });
        }

        function displayClassStudents(students) {
            var container = document.getElementById('classStudentsList');
            var html = '';

            if (students && students.length > 0) {
                students.forEach(function (student) {
                    html += '<div style="margin-bottom: 5px;">';
                    html += '<label style="display: flex; align-items: center; cursor: pointer;">';
                    html += '<input type="checkbox" value="' + student.id + '" style="margin-right: 8px;">';
                    html += '<span>' + student.studentNumber + ' - ' + student.studentName + '</span>';
                    html += '</label>';
                    html += '</div>';
                });
            } else {
                html = '<div style="text-align: center; color: #666; padding: 20px;">暂无学生</div>';
            }

            container.innerHTML = html;
        }

        function displayUnassignedStudents(students) {
            var container = document.getElementById('unassignedStudentsList');
            var html = '';

            if (students && students.length > 0) {
                students.forEach(function (student) {
                    html += '<div style="margin-bottom: 5px;">';
                    html += '<label style="display: flex; align-items: center; cursor: pointer;">';
                    html += '<input type="checkbox" value="' + student.id + '" style="margin-right: 8px;">';
                    html += '<span>' + student.studentNumber + ' - ' + student.studentName + '</span>';
                    html += '</label>';
                    html += '</div>';
                });
            } else {
                html = '<div style="text-align: center; color: #666; padding: 20px;">暂无未分配学生</div>';
            }

            container.innerHTML = html;
        }

        function assignSelectedStudents() {
            var checkboxes = document.querySelectorAll('#unassignedStudentsList input[type="checkbox"]:checked');
            var studentIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

            if (studentIds.length === 0) {
                alert('请选择要分配的学生');
                return;
            }

            fetch('/admin/classes/' + currentClassId + '/students/assign', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    studentIds: studentIds
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadStudentsData(); // 重新加载数据
                    } else {
                        alert('分配失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('分配失败');
                });
        }

        function removeSelectedStudents() {
            var checkboxes = document.querySelectorAll('#classStudentsList input[type="checkbox"]:checked');
            var studentIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

            if (studentIds.length === 0) {
                alert('请选择要移除的学生');
                return;
            }

            if (!confirm('确定要将选中的学生从班级中移除吗？')) {
                return;
            }

            console.log('移除学生IDs:', studentIds); // 调试日志
            console.log('当前班级ID:', currentClassId); // 调试日志

            fetch('/admin/classes/' + currentClassId + '/students/remove', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    studentIds: studentIds
                })
            })
                .then(response => {
                    console.log('响应状态:', response.status); // 调试日志
                    return response.json();
                })
                .then(data => {
                    console.log('响应数据:', data); // 调试日志
                    if (data.success) {
                        alert(data.message);
                        loadStudentsData(); // 重新加载数据
                    } else {
                        alert('移除失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('移除失败：' + error.message);
                });
        }
    </script>
</body>

</html>