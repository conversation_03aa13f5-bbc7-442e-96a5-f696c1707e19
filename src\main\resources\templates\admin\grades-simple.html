<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成绩审核</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .header {
            background-color: #dc3545;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .score-normal {
            color: #28a745;
        }

        .score-warning {
            color: #ffc107;
        }

        .score-danger {
            color: #dc3545;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/admin/dashboard" class="back-btn">← 返回主页</a>

            <h2>成绩审核（简化版）</h2>

            <!-- 错误信息显示 -->
            <div th:if="${error}"
                style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; border: 1px solid #f5c6cb;">
                <strong>错误：</strong> <span th:text="${error}"></span>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>学号</th>
                        <th>学生姓名</th>
                        <th>班级</th>
                        <th>课程名称</th>
                        <th>成绩</th>
                        <th>学期</th>
                        <th>学年</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="grade : ${grades}">
                        <td th:text="${grade.student_number}"></td>
                        <td th:text="${grade.student_name}"></td>
                        <td th:text="${grade.class_name}"></td>
                        <td th:text="${grade.course_name}"></td>
                        <td>
                            <span th:text="${grade.score}"></span>
                        </td>
                        <td th:text="${grade.semester}"></td>
                        <td th:text="${grade.year}"></td>
                        <td>
                            <span th:if="${grade.is_abnormal == 1}" style="color: #dc3545;">异常</span>
                            <span th:unless="${grade.is_abnormal == 1}" style="color: #28a745;">正常</span>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div th:if="${#lists.isEmpty(grades)}" style="text-align: center; padding: 50px; color: #666;">
                暂无成绩记录
            </div>
        </div>
    </div>
</body>

</html>
