<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="simple-grade-system" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="grade-system" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="grade-system" options="-parameters" />
      <module name="simple-grade-system" options="-parameters -parameters" />
    </option>
  </component>
</project>