package com.student.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 课程实体类
 */
@TableName("courses")
public class Course {
    
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("course_name")
    private String courseName;

    @TableField("course_code")
    private String courseCode;

    private Integer credits;

    @TableField("teaching_hours")
    private Integer teachingHours;

    @TableField("teacher_id")
    private Long teacherId;

    private String classroom;
    
    // 构造函数
    public Course() {}
    
    public Course(String courseName, String courseCode, Integer credits, Integer teachingHours) {
        this.courseName = courseName;
        this.courseCode = courseCode;
        this.credits = credits;
        this.teachingHours = teachingHours;
    }

    public Course(String courseName, String courseCode, Integer credits, Integer teachingHours, Long teacherId) {
        this.courseName = courseName;
        this.courseCode = courseCode;
        this.credits = credits;
        this.teachingHours = teachingHours;
        this.teacherId = teacherId;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }
    
    public String getCourseCode() {
        return courseCode;
    }
    
    public void setCourseCode(String courseCode) {
        this.courseCode = courseCode;
    }
    
    public Integer getCredits() {
        return credits;
    }
    
    public void setCredits(Integer credits) {
        this.credits = credits;
    }
    
    public Integer getTeachingHours() {
        return teachingHours;
    }
    
    public void setTeachingHours(Integer teachingHours) {
        this.teachingHours = teachingHours;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public String getClassroom() {
        return classroom;
    }

    public void setClassroom(String classroom) {
        this.classroom = classroom;
    }
}
