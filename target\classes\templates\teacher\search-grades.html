<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成绩查询 - 教师端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .search-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e1e1;
            border-radius: 6px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        tr:hover {
            background-color: #f5f5f5;
        }

        .score {
            font-weight: bold;
            color: #28a745;
        }

        .score.low {
            color: #dc3545;
        }

        .nav-links {
            text-align: left;
            margin-bottom: 20px;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="nav-links">
            <a href="/teacher/dashboard" class="back-btn">← 返回主页</a>
        </div>

        <div class="header">
            <h1>班级成绩查询</h1>
            <p th:text="'欢迎，' + ${user.realName}">欢迎，教师</p>
        </div>

        <div class="search-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="studentName">学生姓名：</label>
                    <input type="text" id="studentName" name="studentName" th:value="${studentName}"
                        placeholder="输入学生姓名" onkeyup="searchTable()">
                </div>
                <div class="form-group">
                    <label for="courseName">课程名称：</label>
                    <input type="text" id="courseName" name="courseName" th:value="${courseName}" placeholder="输入课程名称"
                        onkeyup="searchTable()">
                </div>
                <div class="form-group">
                    <label for="studentNumber">学号：</label>
                    <input type="text" id="studentNumber" name="studentNumber" th:value="${studentNumber}"
                        placeholder="输入学号" onkeyup="searchTable()">
                </div>
                <div class="form-group">
                    <label for="classId">班级：</label>
                    <select id="classId" name="classId" onchange="searchTable()">
                        <option value="">请选择班级</option>
                        <option th:each="clazz : ${classes}" th:value="${clazz.id}" th:text="${clazz.className}"
                            th:selected="${classId != null and classId.toString() == clazz.id.toString()}">班级名称</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="table-container">
            <table th:if="${grades != null and !grades.isEmpty()}">
                <thead>
                    <tr>
                        <th>学生姓名</th>
                        <th>学号</th>
                        <th>课程名称</th>
                        <th>班级</th>
                        <th>成绩</th>
                        <th>学年</th>
                        <th>学期</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="grade : ${grades}" th:data-class-id="${grade.class_id}">
                        <td th:text="${grade.student_name}">学生姓名</td>
                        <td th:text="${grade.student_number}">学号</td>
                        <td th:text="${grade.course_name}">课程名称</td>
                        <td th:text="${grade.class_name}">班级</td>
                        <td>
                            <span th:text="${grade.score}" class="score">成绩</span>
                        </td>
                        <td th:text="${grade.academic_year}">学年</td>
                        <td th:text="${grade.semester}">学期</td>
                    </tr>
                </tbody>
            </table>

            <div th:if="${grades == null or grades.isEmpty()}" class="no-data">
                <p>暂无成绩数据</p>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后设置成绩颜色
        document.addEventListener('DOMContentLoaded', function () {
            setScoreColors();
        });

        function setScoreColors() {
            var scoreElements = document.querySelectorAll('.score');
            scoreElements.forEach(function (element) {
                var scoreText = element.textContent || element.innerText;
                var score = parseFloat(scoreText);
                if (!isNaN(score) && score < 60) {
                    element.classList.add('low');
                }
            });
        }

        function searchTable() {
            var studentNameFilter = document.getElementById("studentName").value.toUpperCase();
            var courseNameFilter = document.getElementById("courseName").value.toUpperCase();
            var studentNumberFilter = document.getElementById("studentNumber").value.toUpperCase();
            var classIdFilter = document.getElementById("classId").value;
            var table = document.querySelector("table");

            if (!table) return;

            var tr = table.getElementsByTagName("tr");

            for (var i = 1; i < tr.length; i++) {
                var studentName = tr[i].getElementsByTagName("td")[0]; // 学生姓名
                var studentNumber = tr[i].getElementsByTagName("td")[1]; // 学号
                var courseName = tr[i].getElementsByTagName("td")[2]; // 课程名称
                var rowClassId = tr[i].getAttribute('data-class-id'); // 班级ID

                if (studentName && studentNumber && courseName) {
                    var studentText = studentName.textContent || studentName.innerText;
                    var numberText = studentNumber.textContent || studentNumber.innerText;
                    var courseText = courseName.textContent || courseName.innerText;

                    // 检查是否匹配所有过滤条件
                    var matchesStudentName = studentNameFilter === "" || studentText.toUpperCase().indexOf(studentNameFilter) > -1;
                    var matchesStudentNumber = studentNumberFilter === "" || numberText.toUpperCase().indexOf(studentNumberFilter) > -1;
                    var matchesCourseName = courseNameFilter === "" || courseText.toUpperCase().indexOf(courseNameFilter) > -1;
                    var matchesClass = classIdFilter === "" || rowClassId === classIdFilter;

                    if (matchesStudentName && matchesStudentNumber && matchesCourseName && matchesClass) {
                        tr[i].style.display = "";
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            }
        }
    </script>
</body>

</html>