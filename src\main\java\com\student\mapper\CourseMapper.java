package com.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.student.entity.Course;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

/**
 * 课程Mapper接口
 */
@Mapper
public interface CourseMapper extends BaseMapper<Course> {

    /**
     * 清空教师的课程
     */
    @Update("UPDATE courses SET teacher_id = NULL WHERE teacher_id = #{teacherId}")
    void clearTeacherFromCourses(Long teacherId);
}
