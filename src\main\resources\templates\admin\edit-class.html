<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑班级 - 管理员</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .header {
            background-color: #dc3545;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }

        .btn-primary {
            background-color: #dc3545;
            color: white;
        }

        .btn-primary:hover {
            background-color: #c82333;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
            text-decoration: none;
        }

        .btn-secondary:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }

        .logout {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .logout:hover {
            background-color: rgba(255, 255, 255, 0.3);
            text-decoration: none;
            color: white;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>学生成绩管理系统</h1>
        <div>
            <span th:text="'欢迎，' + ${user.realName}"></span>
            <a href="/logout" class="logout">退出</a>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <a href="/admin/classes" class="back-btn">← 返回班级管理</a>

            <h2>编辑班级</h2>

            <form method="post" action="/admin/classes/update">
                <input type="hidden" name="classId" th:value="${targetClass.id}">

                <div class="form-group">
                    <label for="className">班级名称：</label>
                    <input type="text" id="className" name="className" required 
                           th:value="${targetClass.className}">
                </div>

                <div class="form-group">
                    <label for="classCode">班级代码：</label>
                    <input type="text" id="classCode" name="classCode" required 
                           th:value="${targetClass.classCode}">
                </div>

                <div class="form-group">
                    <label for="gradeYear">年级：</label>
                    <input type="number" id="gradeYear" name="gradeYear" required 
                           min="2020" max="2030" th:value="${targetClass.gradeYear}">
                </div>

                <div class="form-group">
                    <label for="major">专业：</label>
                    <input type="text" id="major" name="major" required 
                           th:value="${targetClass.major}">
                </div>

                <div class="form-group">
                    <label for="teacherId">班主任：</label>
                    <select id="teacherId" name="teacherId">
                        <option value="">请选择班主任（可选）</option>
                        <option th:each="teacher : ${teachers}" 
                                th:value="${teacher.id}" 
                                th:text="${teacher.realName}"
                                th:selected="${targetClass.teacherId != null and targetClass.teacherId == teacher.id}">
                            教师姓名
                        </option>
                    </select>
                </div>

                <div>
                    <button type="submit" class="btn btn-primary">更新班级</button>
                    <a href="/admin/classes" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</body>

</html>
